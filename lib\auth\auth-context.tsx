"use client";

import React, { createContext, useContext, useEffect, useState, useCallback } from "react";
import { createClient } from "@/lib/supabase/client";
import { clientCreditService } from "@/lib/services/credit-service-client";
import { isEmailAdmin } from "@/lib/auth/admin-config";
import type { SupabaseUser, SupabaseSession, UserType } from "@/app/(auth)/auth";
import type { User } from "@supabase/supabase-js";

interface AuthContextType {
  user: SupabaseUser | null;
  session: SupabaseSession | null;
  credits: number;
  isLoadingAuth: boolean;
  isLoadingCredits: boolean;
  isInitialLoadingCredits: boolean; // For initial load only
  isBackgroundRefreshingCredits: boolean; // For background updates
  creditError: string | null;
  authError: string | null;
  refreshCredits: (background?: boolean) => Promise<void>;
  refreshAuth: () => Promise<void>;
  signOut: () => Promise<void>;
  clearAuthError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [session, setSession] = useState<SupabaseSession | null>(null);
  const [credits, setCredits] = useState<number>(0);
  const [isLoadingAuth, setIsLoadingAuth] = useState(true);
  const [isLoadingCredits, setIsLoadingCredits] = useState(true);
  const [isInitialLoadingCredits, setIsInitialLoadingCredits] = useState(true);
  const [isBackgroundRefreshingCredits, setIsBackgroundRefreshingCredits] = useState(false);
  const [creditError, setCreditError] = useState<string | null>(null);
  const [authError, setAuthError] = useState<string | null>(null);

  const supabase = createClient();

  // Convert Supabase user to our SupabaseUser format
  const convertUser = useCallback((supabaseUser: User | null, userCredits = 0) => {
    if (!supabaseUser) return null;

    const userType: UserType = "regular";
    const isAdmin = isEmailAdmin(supabaseUser.email);

    return {
      id: supabaseUser.id,
      email: supabaseUser.email,
      type: userType,
      credits: userCredits,
      isAdmin,
    };
  }, []);

  // Refresh credit balance
  const refreshCredits = useCallback(
    async (background = false) => {
      if (!user?.id) {
        setCredits(0);
        setIsLoadingCredits(false);
        setIsInitialLoadingCredits(false);
        return;
      }

      // Set appropriate loading state based on whether this is a background refresh
      if (background) {
        setIsBackgroundRefreshingCredits(true);
      } else {
        setIsLoadingCredits(true);
        // Only set initial loading if this is truly the first load
        if (isInitialLoadingCredits) {
          setIsInitialLoadingCredits(true);
        }
      }
      setCreditError(null);

      try {
        const userCredits = await clientCreditService.getUserCredits(user.id);
        setCredits(userCredits);

        // Update user object with new credits
        setUser((prev) => (prev ? { ...prev, credits: userCredits } : null));
      } catch (error) {
        console.error("Failed to refresh credits:", error);
        setCreditError("Failed to load credit balance");
      } finally {
        if (background) {
          setIsBackgroundRefreshingCredits(false);
        } else {
          setIsLoadingCredits(false);
          setIsInitialLoadingCredits(false);
        }
      }
    },
    [user?.id, isInitialLoadingCredits]
  );

  // Clear auth error function
  const clearAuthError = useCallback(() => {
    setAuthError(null);
  }, []);

  // Refresh authentication state following Supabase best practices
  const refreshAuth = useCallback(async () => {
    setIsLoadingAuth(true);
    setAuthError(null);

    try {
      const {
        data: { user: currentUser },
      } = await supabase.auth.getUser();

      if (currentUser) {
        // Get credits for the user
        let userCredits = 0;
        try {
          setIsLoadingCredits(true); // Start loading credits immediately
          setIsInitialLoadingCredits(true);
          userCredits = await clientCreditService.getUserCredits(currentUser.id);
        } catch (error) {
          console.error("[AUTH CONTEXT] Failed to fetch credits during auth refresh:", error);
          // Don't fail auth refresh if credits fail - continue with 0 credits
        } finally {
          setIsLoadingCredits(false);
          setIsInitialLoadingCredits(false);
        }

        const convertedUser = convertUser(currentUser, userCredits);
        setUser(convertedUser);
        setCredits(userCredits);

        if (convertedUser) {
          setSession({ user: convertedUser });
        }
      } else {
        setUser(null);
        setSession(null);
        setCredits(0);
        setIsLoadingCredits(false);
        setIsInitialLoadingCredits(false);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown auth error";
      console.error("[AUTH CONTEXT] Failed to refresh auth:", error);

      setAuthError(errorMessage);
      setUser(null);
      setSession(null);
      setCredits(0);
      setIsLoadingCredits(false);
      setIsInitialLoadingCredits(false);
    } finally {
      setIsLoadingAuth(false);
    }
  }, [convertUser, supabase]);

  // Sign out function following Supabase best practices
  const signOut = useCallback(async () => {
    try {
      await supabase.auth.signOut();
    } catch (error) {
      console.error("[AUTH CONTEXT] Failed to sign out:", error);
    } finally {
      // Always clear local state regardless of signOut success/failure
      setUser(null);
      setSession(null);
      setCredits(0);
      setCreditError(null);
      setAuthError(null);
      setIsLoadingCredits(false);
      setIsInitialLoadingCredits(false);
      clientCreditService.clearCache();
    }
  }, [supabase.auth]);

  // Set up auth state change listener following Supabase best practices
  useEffect(() => {
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, currentSession) => {
      setIsLoadingAuth(true);

      if (event === "SIGNED_IN" || event === "TOKEN_REFRESHED") {
        if (currentSession?.user) {
          // Get credits for the user
          let userCredits = 0;
          try {
            setIsLoadingCredits(true); // Start loading credits immediately
            setIsInitialLoadingCredits(true);
            userCredits = await clientCreditService.getUserCredits(currentSession.user.id);
          } catch (error) {
            console.error("[AUTH CONTEXT] Failed to fetch credits during auth state change:", error);
            // Continue with 0 credits rather than failing
          } finally {
            setIsLoadingCredits(false);
            setIsInitialLoadingCredits(false);
          }

          const convertedUser = convertUser(currentSession.user, userCredits);
          setUser(convertedUser);
          setCredits(userCredits);
          setAuthError(null);

          if (convertedUser) {
            setSession({ user: convertedUser });
          }
        }
      } else if (event === "SIGNED_OUT") {
        setUser(null);
        setSession(null);
        setCredits(0);
        setCreditError(null);
        setAuthError(null);
        setIsLoadingCredits(false);
        setIsInitialLoadingCredits(false);
        clientCreditService.clearCache();
      }

      setIsLoadingAuth(false);
    });

    // Get initial session
    refreshAuth();

    return () => {
      subscription.unsubscribe();
    };
  }, [convertUser, refreshAuth, supabase.auth]);

  // Set up real-time credit updates
  useEffect(() => {
    if (!user?.id) {
      console.log("[AUTH CONTEXT] No user ID, skipping credit subscription");
      return;
    }

    console.log("[AUTH CONTEXT] Setting up credit subscription for user:", user.id);

    const unsubscribe = clientCreditService.subscribeToCredits(user.id, (newCredits) => {
      console.log("[AUTH CONTEXT] Real-time credit update received:", newCredits, "previous:", credits);
      setCredits(newCredits);
      setUser((prev) => (prev ? { ...prev, credits: newCredits } : null));
    });

    return () => {
      console.log("[AUTH CONTEXT] Unsubscribing from credit updates for user:", user.id);
      unsubscribe();
    };
  }, [user?.id, credits]);

  const value: AuthContextType = {
    user,
    session,
    credits,
    isLoadingAuth,
    isLoadingCredits,
    isInitialLoadingCredits,
    isBackgroundRefreshingCredits,
    creditError,
    authError,
    refreshCredits,
    refreshAuth,
    signOut,
    clearAuthError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
