# Task 7: Database Query Caching

**Priority**: Medium
**Estimated Time**: 4 hours
**Status**: Pending
**Dependencies**: Task 2, Task 6

## Objective

Optimize database queries with `unstable_cache` and implement query result caching with proper cache warming strategies.

## Background

Current database issues:
- Frequent queries executed without caching
- No optimization for read-heavy operations
- Missing cache warming for predictable access patterns
- Potential for database performance bottlenecks

## Implementation Plan

### 1. Query-Level Caching (2 hours)

**File**: `lib/db/cached-queries.ts`

Create a dedicated module for cached database queries:

```typescript
import { unstable_cache } from 'next/cache';
import { revalidateTag } from 'next/cache';
import * as queries from './queries';

// Cache user data queries
export const getCachedUser = unstable_cache(
  async (email: string) => {
    return await queries.getUser(email);
  },
  ['user-by-email'],
  {
    revalidate: 300, // 5 minutes
    tags: ['users']
  }
);

// Cache document queries
export const getCachedDocumentsById = unstable_cache(
  async (id: string) => {
    return await queries.getDocumentsById({ id });
  },
  ['documents-by-id'],
  {
    revalidate: 60, // 1 minute
    tags: ['documents']
  }
);

export const getCachedDocumentById = unstable_cache(
  async (id: string) => {
    return await queries.getDocumentById({ id });
  },
  ['document-by-id'],
  {
    revalidate: 60, // 1 minute
    tags: ['documents']
  }
);

// Cache vote queries
export const getCachedVotesByChatId = unstable_cache(
  async (id: string) => {
    return await queries.getVotesByChatId({ id });
  },
  ['votes-by-chat'],
  {
    revalidate: 30, // 30 seconds
    tags: ['votes']
  }
);

// Cache suggestion queries
export const getCachedSuggestionsByDocumentId = unstable_cache(
  async (documentId: string) => {
    return await queries.getSuggestionsByDocumentId({ documentId });
  },
  ['suggestions-by-document'],
  {
    revalidate: 60, // 1 minute
    tags: ['suggestions']
  }
);

// Cache message count queries
export const getCachedMessageCountByUserId = unstable_cache(
  async (id: string, differenceInHours: number) => {
    return await queries.getMessageCountByUserId({ id, differenceInHours });
  },
  ['message-count-by-user'],
  {
    revalidate: 300, // 5 minutes (rate limiting data)
    tags: ['message-counts']
  }
);

// Cache stream IDs
export const getCachedStreamIdsByChatId = unstable_cache(
  async (chatId: string) => {
    return await queries.getStreamIdsByChatId({ chatId });
  },
  ['stream-ids-by-chat'],
  {
    revalidate: 30, // 30 seconds
    tags: ['stream-ids']
  }
);
```

### 2. Cache Warming Service (1.5 hours)

**File**: `lib/db/cache-warmer.ts`

Implement proactive cache warming:

```typescript
import { getCachedUser, getCachedMessageCountByUserId } from './cached-queries';

export class DatabaseCacheWarmer {
  /**
   * Warm user-related caches
   */
  async warmUserCaches(userIds: string[]): Promise<void> {
    const warmingPromises = userIds.map(async (userId) => {
      try {
        // Warm message count cache (used for rate limiting)
        await getCachedMessageCountByUserId(userId, 24);
        
        console.log(`Warmed cache for user: ${userId}`);
      } catch (error) {
        console.warn(`Failed to warm cache for user ${userId}:`, error);
      }
    });
    
    await Promise.allSettled(warmingPromises);
  }
  
  /**
   * Warm chat-related caches
   */
  async warmChatCaches(chatIds: string[]): Promise<void> {
    const warmingPromises = chatIds.map(async (chatId) => {
      try {
        // Import cached queries dynamically to avoid circular dependencies
        const { getCachedVotesByChatId, getCachedStreamIdsByChatId } = await import('./cached-queries');
        
        await Promise.all([
          getCachedVotesByChatId(chatId),
          getCachedStreamIdsByChatId(chatId)
        ]);
        
        console.log(`Warmed cache for chat: ${chatId}`);
      } catch (error) {
        console.warn(`Failed to warm cache for chat ${chatId}:`, error);
      }
    });
    
    await Promise.allSettled(warmingPromises);
  }
  
  /**
   * Warm document-related caches
   */
  async warmDocumentCaches(documentIds: string[]): Promise<void> {
    const warmingPromises = documentIds.map(async (documentId) => {
      try {
        const { getCachedDocumentsById, getCachedSuggestionsByDocumentId } = await import('./cached-queries');
        
        await Promise.all([
          getCachedDocumentsById(documentId),
          getCachedSuggestionsByDocumentId(documentId)
        ]);
        
        console.log(`Warmed cache for document: ${documentId}`);
      } catch (error) {
        console.warn(`Failed to warm cache for document ${documentId}:`, error);
      }
    });
    
    await Promise.allSettled(warmingPromises);
  }
  
  /**
   * Warm caches for active users based on recent activity
   */
  async warmActiveUserCaches(): Promise<void> {
    try {
      // Get recently active users (last 24 hours)
      const recentChats = await queries.getChatsByUserId({
        id: '', // This would need to be modified to get recent chats across all users
        limit: 50
      });
      
      const activeUserIds = [...new Set(recentChats.chats.map(chat => chat.userId))];
      
      await this.warmUserCaches(activeUserIds);
      
      console.log(`Warmed caches for ${activeUserIds.length} active users`);
    } catch (error) {
      console.error('Failed to warm active user caches:', error);
    }
  }
}

// Singleton instance
let cacheWarmer: DatabaseCacheWarmer | null = null;

export function getCacheWarmer(): DatabaseCacheWarmer {
  if (!cacheWarmer) {
    cacheWarmer = new DatabaseCacheWarmer();
  }
  return cacheWarmer;
}
```

### 3. Query Optimization Integration (0.5 hours)

**File**: `lib/db/queries.ts`

Add cache invalidation to existing write operations:

```typescript
import { revalidateTag } from 'next/cache';

// Enhanced save operations with cache invalidation
export async function saveDocumentWithCacheInvalidation(documentData: any) {
  const result = await saveDocument(documentData);
  
  // Invalidate document caches
  revalidateTag('documents');
  revalidateTag(`documents-${documentData.id}`);
  
  return result;
}

export async function saveSuggestionsWithCacheInvalidation(suggestions: Array<any>) {
  const result = await saveSuggestions({ suggestions });
  
  // Invalidate suggestion caches
  revalidateTag('suggestions');
  
  // Invalidate document-specific caches
  const documentIds = [...new Set(suggestions.map(s => s.documentId))];
  for (const docId of documentIds) {
    revalidateTag(`suggestions-${docId}`);
  }
  
  return result;
}

export async function voteMessageWithCacheInvalidation(voteData: any) {
  const result = await voteMessage(voteData);
  
  // Invalidate vote caches
  revalidateTag('votes');
  revalidateTag(`votes-${voteData.chatId}`);
  
  return result;
}
```

## Code Changes

### 1. Update Chat Route to Use Cached Queries

**File**: `app/(chat)/api/chat/route.ts`
```typescript
import { getCachedMessageCountByUserId } from '@/lib/db/cached-queries';

export async function POST(request: Request) {
  // ... existing setup ...
  
  // Use cached message count query
  const messageCount = await getCachedMessageCountByUserId(
    session.user.id,
    24
  );
  
  if (messageCount > entitlementsByUserType[userType].maxMessagesPerDay) {
    return new ChatSDKError("rate_limit:chat").toResponse();
  }
  
  // ... rest of implementation
}
```

### 2. Update Document API Routes

**File**: `app/api/document/route.ts`
```typescript
import { getCachedDocumentsById, saveDocumentWithCacheInvalidation } from '@/lib/db/cached-queries';

export async function GET(request: Request) {
  // ... existing setup ...
  
  // Use cached document query
  const documents = await getCachedDocumentsById(id);
  
  return Response.json(documents);
}

export async function POST(request: Request) {
  // ... existing setup ...
  
  // Use cache-aware save operation
  const savedDocument = await saveDocumentWithCacheInvalidation({
    id,
    title,
    content,
    kind,
    userId: session.user.id,
  });
  
  return Response.json(savedDocument);
}
```

### 3. Cache Warming Scheduler

**File**: `lib/db/cache-scheduler.ts`
```typescript
import { getCacheWarmer } from './cache-warmer';

export class CacheScheduler {
  private intervalId: NodeJS.Timeout | null = null;
  
  /**
   * Start periodic cache warming
   */
  start(intervalMinutes: number = 15): void {
    if (this.intervalId) {
      console.warn('Cache scheduler already running');
      return;
    }
    
    const intervalMs = intervalMinutes * 60 * 1000;
    
    this.intervalId = setInterval(async () => {
      try {
        const cacheWarmer = getCacheWarmer();
        await cacheWarmer.warmActiveUserCaches();
      } catch (error) {
        console.error('Scheduled cache warming failed:', error);
      }
    }, intervalMs);
    
    console.log(`Cache scheduler started with ${intervalMinutes} minute interval`);
  }
  
  /**
   * Stop periodic cache warming
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.log('Cache scheduler stopped');
    }
  }
}

// Export singleton
export const cacheScheduler = new CacheScheduler();
```

## Testing Strategy

1. **Cache Performance Testing**:
   - Measure query response times with/without caching
   - Test cache hit rates for different query types
   - Monitor database load reduction

2. **Cache Warming Testing**:
   - Verify cache warming improves response times
   - Test warming strategies for different usage patterns
   - Monitor warming overhead

3. **Cache Invalidation Testing**:
   - Ensure write operations properly invalidate caches
   - Test cache consistency after updates
   - Verify no stale data persists

## Success Criteria

- [ ] Database query performance improved by 50%+
- [ ] Cache hit rate > 75% for frequently accessed data
- [ ] Cache warming reduces cold start latency
- [ ] No cache consistency issues
- [ ] Database load measurably reduced

## Rollback Plan

1. Remove `unstable_cache` wrappers from queries
2. Disable cache warming service
3. Return to direct database queries
4. Monitor for any performance regressions

## Next Steps

- Move to Task 8: API Route Cache Headers
- Implement database performance monitoring
- Fine-tune cache warming schedules

## Notes

- Monitor cache memory usage
- Adjust TTL values based on usage patterns
- Consider cache warming for predictable access patterns
- Test thoroughly under load conditions
