// Route segment configuration for authentication
export const dynamic = "force-dynamic";
export const revalidate = 0;

("use client");

import Link from "next/link";
import { useRouter } from "next/navigation";
import { useActionState, useEffect, useState } from "react";
import { toast } from "@/components/toast";

import { AuthForm } from "@/components/auth-form";
import { SubmitButton } from "@/components/submit-button";
import { GoogleSignInButton } from "@/components/google-sign-in-button";

import { login, type LoginActionState } from "../actions";
import { createClient } from "@/lib/supabase/client";
import { trackLogin } from "@/lib/analytics";

export default function Page() {
  const router = useRouter();

  const [email, setEmail] = useState("");
  const [isSuccessful, setIsSuccessful] = useState(false);

  const [state, formAction] = useActionState<LoginActionState, FormData>(login, {
    status: "idle",
  });

  const supabase = createClient();

  useEffect(() => {
    // Guard against undefined state
    if (!state) return;

    if (state.status === "failed") {
      toast({
        type: "error",
        description: "Invalid credentials!",
      });
    } else if (state.status === "invalid_data") {
      toast({
        type: "error",
        description: "Failed validating your submission!",
      });
    } else if (state.status === "email_not_confirmed" && state.email) {
      toast({
        type: "error",
        description: "Your email is not verified. Redirecting to verification page...",
      });

      // Redirect to the verification page with the email as a query parameter
      setTimeout(() => {
        router.push(`/auth/verify-email?email=${encodeURIComponent(state.email || "")}`);
      }, 1500);
    } else if (state.status === "success") {
      // Show success toast and set successful state
      toast({ type: "success", description: "Login successful!" });
      setIsSuccessful(true);

      // Identify user in PostHog and track login
      const supabase = createClient();
      supabase.auth.getUser().then(({ data }) => {
        if (data.user) {
          const userType = data.user.email && /^guest-\d+$/.test(data.user.email) ? "guest" : "regular";
          // Check if the user has a Google identity
          const provider = data.user.app_metadata?.provider === "google" ? "google" : "email";
          trackLogin(data.user.id, userType, provider);
        }
      });

      // Simply refresh the page - the middleware will handle redirection
      setTimeout(() => {
        window.location.reload();
      }, 1000); // Short delay to allow the toast to be seen
    }
  }, [state, router]);

  const handleSubmit = (formData: FormData) => {
    setEmail(formData.get("email") as string);
    formAction(formData);
  };

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h1 className="app-title dark:text-zinc-50">Bát Tự Master V</h1>
          <h2 className="text-lg text-gray-700 dark:text-zinc-300 mb-4">Phán về số mệnh, cuộc đời, tình duyên, công việc</h2>
          <h3 className="text-xl font-semibold dark:text-zinc-50">Sign In</h3>
          <p className="text-sm text-gray-500 dark:text-zinc-400">Use your email and password to sign in</p>
        </div>
        <AuthForm action={handleSubmit} defaultEmail={email}>
          <SubmitButton isSuccessful={isSuccessful}>Sign in</SubmitButton>

          <div className="relative my-4">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300 dark:border-gray-700" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-background text-gray-500 dark:text-gray-400">Or continue with</span>
            </div>
          </div>

          <GoogleSignInButton />

          <p className="text-center text-sm text-gray-600 mt-4 dark:text-zinc-400">
            {"Don't have an account? "}
            <Link href="/register" className="font-semibold text-gray-800 hover:underline dark:text-zinc-200">
              Sign up
            </Link>
            {" for free."}
          </p>
        </AuthForm>
      </div>
    </div>
  );
}
