import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { AppSidebar } from "@/components/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { getServerSession } from "../(auth)/auth-server";
import { AuthProviderWrapper } from "@/components/auth-provider-wrapper";
import Script from "next/script";

// Route segment configuration for chat layout
export const experimental_ppr = true;
export const revalidate = 300; // 5 minutes for user session data
export const fetchCache = "default-cache";

export default async function Layout({ children }: { children: React.ReactNode }) {
  const session = await getServerSession();

  // Strict authentication check - redirect if no session
  if (!session || !session.user) {
    console.log("Chat layout: No session or user, redirecting to login");
    redirect("/login");
  }

  // Check if user is a guest (should be caught by middleware, but double-check)
  const isGuest = session.user.email && /^guest-\d+$/.test(session.user.email);
  if (isGuest) {
    console.log("Chat layout: Guest user detected, redirecting to login");
    redirect("/login");
  }

  console.log("Chat layout: Authenticated user, rendering chat layout");

  const cookieStore = await cookies();
  const isCollapsed = cookieStore.get("sidebar:state")?.value !== "true";

  return (
    <>
      <Script src="https://cdn.jsdelivr.net/pyodide/v0.23.4/full/pyodide.js" strategy="beforeInteractive" />
      <AuthProviderWrapper>
        <SidebarProvider defaultOpen={!isCollapsed}>
          <AppSidebar user={session.user} />
          <SidebarInset>{children}</SidebarInset>
        </SidebarProvider>
      </AuthProviderWrapper>
    </>
  );
}
