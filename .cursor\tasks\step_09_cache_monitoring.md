# Task 9: Cache Performance Monitoring

**Priority**: Medium
**Estimated Time**: 4 hours
**Status**: Pending
**Dependencies**: All previous tasks

## Objective

Implement comprehensive cache performance monitoring with metrics collection, performance logging, and cache health checks.

## Background

Need for monitoring:
- Track cache hit/miss rates across different layers
- Monitor cache performance impact
- Identify optimization opportunities
- Detect cache-related issues early

## Implementation Plan

### 1. Cache Metrics Collection (2 hours)

**File**: `lib/monitoring/cache-metrics.ts`

Create comprehensive cache monitoring:

```typescript
interface CacheMetrics {
  hits: number;
  misses: number;
  errors: number;
  totalRequests: number;
  averageResponseTime: number;
  lastUpdated: Date;
}

interface CacheLayerMetrics {
  nextjs: CacheMetrics;
  redis: CacheMetrics;
  database: CacheMetrics;
}

export class CacheMonitor {
  private metrics: Map<string, CacheLayerMetrics> = new Map();
  private performanceEntries: Map<string, number[]> = new Map();
  
  /**
   * Record cache hit
   */
  recordHit(layer: keyof CacheLayerMetrics, key: string, responseTime: number) {
    const metrics = this.getOrCreateMetrics(key);
    metrics[layer].hits++;
    metrics[layer].totalRequests++;
    this.updateAverageResponseTime(key, layer, responseTime);
    this.updateLastUpdated(key, layer);
  }
  
  /**
   * Record cache miss
   */
  recordMiss(layer: keyof CacheLayerMetrics, key: string, responseTime: number) {
    const metrics = this.getOrCreateMetrics(key);
    metrics[layer].misses++;
    metrics[layer].totalRequests++;
    this.updateAverageResponseTime(key, layer, responseTime);
    this.updateLastUpdated(key, layer);
  }
  
  /**
   * Record cache error
   */
  recordError(layer: keyof CacheLayerMetrics, key: string) {
    const metrics = this.getOrCreateMetrics(key);
    metrics[layer].errors++;
    metrics[layer].totalRequests++;
    this.updateLastUpdated(key, layer);
  }
  
  /**
   * Get cache hit rate for a specific key and layer
   */
  getHitRate(key: string, layer: keyof CacheLayerMetrics): number {
    const metrics = this.metrics.get(key);
    if (!metrics || !metrics[layer]) return 0;
    
    const layerMetrics = metrics[layer];
    if (layerMetrics.totalRequests === 0) return 0;
    
    return layerMetrics.hits / layerMetrics.totalRequests;
  }
  
  /**
   * Get overall cache statistics
   */
  getOverallStats(): {
    totalHits: number;
    totalMisses: number;
    totalErrors: number;
    overallHitRate: number;
    layerStats: Record<keyof CacheLayerMetrics, {
      hits: number;
      misses: number;
      errors: number;
      hitRate: number;
    }>;
  } {
    let totalHits = 0;
    let totalMisses = 0;
    let totalErrors = 0;
    
    const layerStats = {
      nextjs: { hits: 0, misses: 0, errors: 0, hitRate: 0 },
      redis: { hits: 0, misses: 0, errors: 0, hitRate: 0 },
      database: { hits: 0, misses: 0, errors: 0, hitRate: 0 }
    };
    
    for (const metrics of this.metrics.values()) {
      for (const [layer, layerMetrics] of Object.entries(metrics)) {
        const layerKey = layer as keyof CacheLayerMetrics;
        layerStats[layerKey].hits += layerMetrics.hits;
        layerStats[layerKey].misses += layerMetrics.misses;
        layerStats[layerKey].errors += layerMetrics.errors;
        
        totalHits += layerMetrics.hits;
        totalMisses += layerMetrics.misses;
        totalErrors += layerMetrics.errors;
      }
    }
    
    // Calculate hit rates
    for (const layer of Object.keys(layerStats) as Array<keyof CacheLayerMetrics>) {
      const total = layerStats[layer].hits + layerStats[layer].misses;
      layerStats[layer].hitRate = total > 0 ? layerStats[layer].hits / total : 0;
    }
    
    const overallTotal = totalHits + totalMisses;
    const overallHitRate = overallTotal > 0 ? totalHits / overallTotal : 0;
    
    return {
      totalHits,
      totalMisses,
      totalErrors,
      overallHitRate,
      layerStats
    };
  }
  
  /**
   * Export metrics for external monitoring
   */
  exportMetrics(): Record<string, any> {
    const stats = this.getOverallStats();
    
    return {
      timestamp: new Date().toISOString(),
      cache: {
        overall: {
          hitRate: stats.overallHitRate,
          totalRequests: stats.totalHits + stats.totalMisses,
          errors: stats.totalErrors
        },
        layers: stats.layerStats
      },
      performance: this.getPerformanceStats()
    };
  }
  
  private getOrCreateMetrics(key: string): CacheLayerMetrics {
    if (!this.metrics.has(key)) {
      this.metrics.set(key, {
        nextjs: { hits: 0, misses: 0, errors: 0, totalRequests: 0, averageResponseTime: 0, lastUpdated: new Date() },
        redis: { hits: 0, misses: 0, errors: 0, totalRequests: 0, averageResponseTime: 0, lastUpdated: new Date() },
        database: { hits: 0, misses: 0, errors: 0, totalRequests: 0, averageResponseTime: 0, lastUpdated: new Date() }
      });
    }
    return this.metrics.get(key)!;
  }
  
  private updateAverageResponseTime(key: string, layer: keyof CacheLayerMetrics, responseTime: number) {
    const metrics = this.metrics.get(key)!;
    const layerMetrics = metrics[layer];
    
    // Simple moving average
    const currentAvg = layerMetrics.averageResponseTime;
    const totalRequests = layerMetrics.totalRequests;
    
    layerMetrics.averageResponseTime = 
      (currentAvg * (totalRequests - 1) + responseTime) / totalRequests;
  }
  
  private updateLastUpdated(key: string, layer: keyof CacheLayerMetrics) {
    const metrics = this.metrics.get(key)!;
    metrics[layer].lastUpdated = new Date();
  }
  
  private getPerformanceStats() {
    const stats: Record<string, any> = {};
    
    for (const [key, times] of this.performanceEntries) {
      if (times.length > 0) {
        stats[key] = {
          min: Math.min(...times),
          max: Math.max(...times),
          avg: times.reduce((a, b) => a + b, 0) / times.length,
          count: times.length
        };
      }
    }
    
    return stats;
  }
}

// Singleton instance
let cacheMonitor: CacheMonitor | null = null;

export function getCacheMonitor(): CacheMonitor {
  if (!cacheMonitor) {
    cacheMonitor = new CacheMonitor();
  }
  return cacheMonitor;
}
```

### 2. Performance Logging Integration (1.5 hours)

**File**: `lib/monitoring/cache-logger.ts`

Create detailed cache performance logging:

```typescript
import { getCacheMonitor } from './cache-metrics';

export class CacheLogger {
  private monitor = getCacheMonitor();
  
  /**
   * Log cache operation with timing
   */
  async logCacheOperation<T>(
    operation: string,
    layer: 'nextjs' | 'redis' | 'database',
    key: string,
    fn: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await fn();
      const responseTime = Date.now() - startTime;
      
      // Determine if it was a hit or miss based on result
      const isHit = result !== null && result !== undefined;
      
      if (isHit) {
        this.monitor.recordHit(layer, key, responseTime);
        console.log(`[CACHE HIT] ${layer}:${operation}:${key} (${responseTime}ms)`);
      } else {
        this.monitor.recordMiss(layer, key, responseTime);
        console.log(`[CACHE MISS] ${layer}:${operation}:${key} (${responseTime}ms)`);
      }
      
      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.monitor.recordError(layer, key);
      console.error(`[CACHE ERROR] ${layer}:${operation}:${key} (${responseTime}ms):`, error);
      throw error;
    }
  }
  
  /**
   * Log cache invalidation
   */
  logCacheInvalidation(type: string, targets: string[]) {
    console.log(`[CACHE INVALIDATION] ${type}: ${targets.join(', ')}`);
  }
  
  /**
   * Log cache warming
   */
  logCacheWarming(type: string, count: number, duration: number) {
    console.log(`[CACHE WARMING] ${type}: ${count} items warmed in ${duration}ms`);
  }
  
  /**
   * Generate performance report
   */
  generatePerformanceReport(): string {
    const stats = this.monitor.getOverallStats();
    
    const report = [
      '=== Cache Performance Report ===',
      `Overall Hit Rate: ${(stats.overallHitRate * 100).toFixed(2)}%`,
      `Total Requests: ${stats.totalHits + stats.totalMisses}`,
      `Total Errors: ${stats.totalErrors}`,
      '',
      'Layer Breakdown:',
      ...Object.entries(stats.layerStats).map(([layer, layerStats]) => 
        `  ${layer}: ${(layerStats.hitRate * 100).toFixed(2)}% hit rate (${layerStats.hits + layerStats.misses} requests)`
      ),
      '================================'
    ];
    
    return report.join('\n');
  }
}

// Singleton instance
let cacheLogger: CacheLogger | null = null;

export function getCacheLogger(): CacheLogger {
  if (!cacheLogger) {
    cacheLogger = new CacheLogger();
  }
  return cacheLogger;
}
```

### 3. Health Check API (0.5 hours)

**File**: `app/api/cache/health/route.ts`

Create cache health monitoring endpoint:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getCacheMonitor } from '@/lib/monitoring/cache-metrics';
import { getServerSession } from '@/app/(auth)/auth-server';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    
    // Only allow admin access to cache health
    if (!session?.user?.isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const monitor = getCacheMonitor();
    const metrics = monitor.exportMetrics();
    
    // Add health status
    const health = {
      status: 'healthy',
      checks: {
        overallHitRate: {
          status: metrics.cache.overall.hitRate > 0.7 ? 'healthy' : 'warning',
          value: metrics.cache.overall.hitRate,
          threshold: 0.7
        },
        errorRate: {
          status: metrics.cache.overall.errors < 10 ? 'healthy' : 'warning',
          value: metrics.cache.overall.errors,
          threshold: 10
        }
      }
    };
    
    return NextResponse.json({
      health,
      metrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Cache health check error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

## Code Changes

### 1. Integrate Monitoring into Credit Service

**File**: `lib/services/credit-service.ts`
```typescript
import { getCacheLogger } from '@/lib/monitoring/cache-logger';

export class ServerCreditService implements ICreditService {
  private cacheLogger = getCacheLogger();
  
  async getUserCredits(userId: string): Promise<number> {
    // Try Redis cache with monitoring
    const redisCredits = await this.cacheLogger.logCacheOperation(
      'getUserCredits',
      'redis',
      `credits-${userId}`,
      () => this.getCachedCredits(userId)
    );
    
    if (redisCredits !== null) {
      return redisCredits;
    }
    
    // Fetch from database with monitoring
    const freshCredits = await this.cacheLogger.logCacheOperation(
      'getUserCredits',
      'database',
      `credits-${userId}`,
      () => this.fetchCreditsFromDatabase(userId)
    );
    
    // Cache the result
    await this.setCachedCredits(userId, freshCredits);
    
    return freshCredits;
  }
}
```

### 2. Add Monitoring to Database Queries

**File**: `lib/db/cached-queries.ts`
```typescript
import { getCacheLogger } from '@/lib/monitoring/cache-logger';

const cacheLogger = getCacheLogger();

export const getCachedChatById = unstable_cache(
  async (id: string) => {
    return await cacheLogger.logCacheOperation(
      'getChatById',
      'database',
      `chat-${id}`,
      async () => {
        const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));
        return selectedChat;
      }
    );
  },
  ['chat-metadata'],
  {
    revalidate: 300,
    tags: ['chat-metadata']
  }
);
```

### 3. Performance Dashboard Component

**File**: `components/admin/cache-dashboard.tsx`
```typescript
"use client";

import { useEffect, useState } from 'react';

interface CacheHealth {
  health: any;
  metrics: any;
  timestamp: string;
}

export function CacheDashboard() {
  const [cacheHealth, setCacheHealth] = useState<CacheHealth | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const fetchCacheHealth = async () => {
      try {
        const response = await fetch('/api/cache/health');
        if (response.ok) {
          const data = await response.json();
          setCacheHealth(data);
        }
      } catch (error) {
        console.error('Failed to fetch cache health:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCacheHealth();
    const interval = setInterval(fetchCacheHealth, 30000); // Update every 30 seconds
    
    return () => clearInterval(interval);
  }, []);
  
  if (loading) return <div>Loading cache metrics...</div>;
  if (!cacheHealth) return <div>Failed to load cache metrics</div>;
  
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Cache Performance Dashboard</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold">Overall Hit Rate</h3>
          <p className="text-3xl font-bold text-green-600">
            {(cacheHealth.metrics.cache.overall.hitRate * 100).toFixed(1)}%
          </p>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold">Total Requests</h3>
          <p className="text-3xl font-bold text-blue-600">
            {cacheHealth.metrics.cache.overall.totalRequests}
          </p>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold">Errors</h3>
          <p className="text-3xl font-bold text-red-600">
            {cacheHealth.metrics.cache.overall.errors}
          </p>
        </div>
      </div>
      
      <div className="bg-white p-4 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">Layer Performance</h3>
        <div className="space-y-2">
          {Object.entries(cacheHealth.metrics.cache.layers).map(([layer, stats]: [string, any]) => (
            <div key={layer} className="flex justify-between items-center">
              <span className="capitalize">{layer}</span>
              <span className="font-semibold">
                {(stats.hitRate * 100).toFixed(1)}% ({stats.hits + stats.misses} requests)
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
```

## Testing Strategy

1. **Metrics Accuracy Testing**:
   - Verify hit/miss counting accuracy
   - Test performance timing measurements
   - Validate error tracking

2. **Dashboard Testing**:
   - Test real-time metric updates
   - Verify admin access controls
   - Test dashboard responsiveness

3. **Performance Impact Testing**:
   - Measure monitoring overhead
   - Test with high load scenarios
   - Verify monitoring doesn't affect performance

## Success Criteria

- [ ] Accurate cache hit/miss tracking
- [ ] Real-time performance monitoring
- [ ] Admin dashboard functional
- [ ] Monitoring overhead < 5%
- [ ] Health checks working correctly

## Rollback Plan

1. Disable cache monitoring
2. Remove performance logging
3. Return to basic console logging
4. Monitor for any performance impact

## Next Steps

- Move to Task 10: Static Generation Optimization
- Set up alerting for cache performance issues
- Fine-tune monitoring thresholds

## Notes

- Monitor the monitoring overhead carefully
- Consider sampling for high-traffic scenarios
- Ensure monitoring doesn't impact user experience
- Set up alerts for critical cache issues
