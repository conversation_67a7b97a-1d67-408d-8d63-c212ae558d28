"use client";

import { Coins } from "lucide-react";
import { useCreditDisplay } from "@/lib/hooks/use-credits";
import { LoaderIcon } from "@/components/icons";
import { cn } from "@/lib/utils";

// Size classes for reuse
const sizeClasses = {
  sm: {
    container: "px-2 py-1 text-xs",
    icon: "size-3",
    text: "text-xs font-medium",
  },
  md: {
    container: "px-3 py-1.5 text-sm",
    icon: "size-4",
    text: "text-sm font-medium",
  },
  lg: {
    container: "px-4 py-2 text-base",
    icon: "size-5",
    text: "text-base font-semibold",
  },
};

interface CreditDisplayProps {
  className?: string;
  showIcon?: boolean;
  size?: "sm" | "md" | "lg";
}

export function CreditDisplay({ className, showIcon = true, size = "sm" }: CreditDisplayProps) {
  const { credits, creditDisplayText, creditBadgeColor, isLowCredits, isOutOfCredits, isBackgroundRefreshingCredits } = useCreditDisplay();

  // Color classes based on credit level
  const getColorClasses = () => {
    if (isOutOfCredits) {
      return "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800";
    }
    if (isLowCredits) {
      return "bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800";
    }
    return "bg-emerald-100 text-emerald-800 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-400 dark:border-emerald-800";
  };

  const currentSize = sizeClasses[size];

  return (
    <div
      className={cn(
        "inline-flex items-center gap-1.5 rounded-full border transition-colors",
        currentSize.container,
        getColorClasses(),
        isBackgroundRefreshingCredits && "opacity-75", // Subtle indicator for background refresh
        className
      )}
      title={`${credits} credits remaining${isBackgroundRefreshingCredits ? " (updating...)" : ""}`}
    >
      {showIcon && (
        <Coins
          className={cn(
            currentSize.icon,
            "flex-shrink-0",
            isBackgroundRefreshingCredits && "animate-pulse" // Subtle animation during background refresh
          )}
        />
      )}
      <span className={cn(currentSize.text, "tabular-nums")}>{creditDisplayText}</span>
    </div>
  );
}

interface CreditDisplayWithLoadingProps extends CreditDisplayProps {
  isLoading?: boolean;
}

export function CreditDisplayWithLoading({ isLoading, className, showIcon = true, size = "sm", ...props }: CreditDisplayWithLoadingProps) {
  const currentSize = sizeClasses[size];

  if (isLoading) {
    return (
      <div className={cn("inline-flex items-center gap-1.5 rounded-full border bg-muted/50 text-muted-foreground", currentSize.container, className)}>
        {showIcon && <LoaderIcon size={currentSize.icon === "size-3" ? 12 : currentSize.icon === "size-4" ? 16 : 20} />}
        <span className={cn(currentSize.text)}>Loading...</span>
      </div>
    );
  }

  return <CreditDisplay className={className} showIcon={showIcon} size={size} {...props} />;
}
