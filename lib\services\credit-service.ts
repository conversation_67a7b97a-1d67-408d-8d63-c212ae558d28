/**
 * Enhanced Credit Service Layer
 * Provides atomic credit operations with caching, rate limiting, and comprehensive error handling
 */

import { createClient as createServer<PERSON>lient } from "@/lib/supabase/server";
import { createClient as createBrowserClient } from "@/lib/supabase/client";
import { getRedisClient } from "@/lib/redis/client";
import type { CreditTransaction, CreditDeductionResult, CreditAdditionResult, CreditCacheEntry, CreditRateLimit } from "@/lib/types/credit";
import { CREDIT_CACHE_CONFIG, CREDIT_RATE_LIMITS, CreditRateLimitError, CreditOperationError, CREDIT_CONSTANTS } from "@/lib/types/credit";
import { ChatSDKError } from "@/lib/errors";

/**
 * Base Credit Service Interface
 * Defines the contract for both server and client implementations
 */
interface ICreditService {
  deductCredit(userId: string, amount: number, description: string): Promise<CreditDeductionResult>;
  getUserCredits(userId: string): Promise<number>;
  addCredits(userId: string, amount: number, description: string): Promise<CreditAdditionResult>;
  getCreditHistory(userId: string, limit?: number, offset?: number): Promise<CreditTransaction[]>;
  validateSufficientCredits(userId: string, amount: number): Promise<boolean>;
}

/**
 * Server-side Credit Service
 * Used in API routes and server actions with Redis caching and rate limiting
 */
export class ServerCreditService implements ICreditService {
  private redis: Awaited<ReturnType<typeof getRedisClient>> | null = null;
  private redisInitialized = false;

  constructor() {}

  private async ensureRedisInitialized() {
    if (!this.redisInitialized) {
      await this.initializeSharedRedis();
    }
  }

  private async initializeSharedRedis() {
    if (this.redisInitialized) return;
    try {
      const client = await getRedisClient();
      if (client) {
        this.redis = client;
        this.redisInitialized = true;
        console.log("Shared Redis client obtained for credit service");
      } else {
        console.warn("Failed to get shared Redis client for credit service, credit caching/rate limiting might be disabled");
        this.redis = null;
        this.redisInitialized = true;
      }
    } catch (error) {
      console.error("Error obtaining shared Redis for credit service:", error);
      this.redis = null;
      this.redisInitialized = true;
    }
  }

  /**
   * Check rate limits for credit operations
   */
  private async checkRateLimit(userId: string): Promise<void> {
    await this.ensureRedisInitialized();
    if (!this.redis) return;

    const now = Date.now();
    const minuteKey = `${CREDIT_CACHE_CONFIG.RATE_LIMIT_KEY_PREFIX}${userId}:minute`;
    const hourKey = `${CREDIT_CACHE_CONFIG.RATE_LIMIT_KEY_PREFIX}${userId}:hour`;

    try {
      // Check minute limit
      const minuteCount = await this.redis.incr(minuteKey);
      if (minuteCount === 1) {
        await this.redis.expire(minuteKey, 60); // 1 minute TTL
      }

      if (minuteCount > CREDIT_RATE_LIMITS.maxDeductionsPerMinute) {
        const resetTime = new Date(now + 60000); // 1 minute from now
        throw new CreditRateLimitError("minute", minuteCount, CREDIT_RATE_LIMITS.maxDeductionsPerMinute, resetTime);
      }

      // Check hour limit
      const hourCount = await this.redis.incr(hourKey);
      if (hourCount === 1) {
        await this.redis.expire(hourKey, 3600); // 1 hour TTL
      }

      if (hourCount > CREDIT_RATE_LIMITS.maxDeductionsPerHour) {
        const resetTime = new Date(now + 3600000); // 1 hour from now
        throw new CreditRateLimitError("hour", hourCount, CREDIT_RATE_LIMITS.maxDeductionsPerHour, resetTime);
      }
    } catch (error) {
      if (error instanceof CreditRateLimitError) {
        throw error;
      }
      console.error("Rate limit check failed:", error);
      // Continue without rate limiting if Redis fails
    }
  }

  /**
   * Get cached credit balance
   */
  private async getCachedCredits(userId: string): Promise<number | null> {
    await this.ensureRedisInitialized();
    if (!this.redis) return null;

    try {
      const cacheKey = `${CREDIT_CACHE_CONFIG.REDIS_KEY_PREFIX}${userId}`;
      const cached = await this.redis.get(cacheKey);

      if (cached) {
        const entry: CreditCacheEntry = JSON.parse(cached);
        const now = Date.now();

        if (now - entry.timestamp < entry.ttl * 1000) {
          return entry.credits;
        } else {
          // Cache expired, remove it
          await this.redis.del(cacheKey);
        }
      }
    } catch (error) {
      console.error("Cache read failed:", error);
    }

    return null;
  }

  /**
   * Cache credit balance
   */
  private async setCachedCredits(userId: string, credits: number): Promise<void> {
    await this.ensureRedisInitialized();
    if (!this.redis) return;

    try {
      const cacheKey = `${CREDIT_CACHE_CONFIG.REDIS_KEY_PREFIX}${userId}`;
      const entry: CreditCacheEntry = {
        credits,
        timestamp: Date.now(),
        ttl: CREDIT_CACHE_CONFIG.TTL_SECONDS,
      };

      await this.redis.setEx(cacheKey, CREDIT_CACHE_CONFIG.TTL_SECONDS, JSON.stringify(entry));
    } catch (error) {
      console.error("Cache write failed:", error);
    }
  }

  /**
   * Invalidate cached credit balance
   */
  private async invalidateCache(userId: string): Promise<void> {
    await this.ensureRedisInitialized();
    if (!this.redis) return;

    try {
      const cacheKey = `${CREDIT_CACHE_CONFIG.REDIS_KEY_PREFIX}${userId}`;
      await this.redis.del(cacheKey);
    } catch (error) {
      console.error("Cache invalidation failed:", error);
    }
  }

  /**
   * Public method to invalidate cache for external use (e.g., admin operations)
   */
  async invalidateUserCache(userId: string): Promise<void> {
    await this.invalidateCache(userId);
  }

  async deductCredit(userId: string, amount: number, description: string): Promise<CreditDeductionResult> {
    try {
      await this.ensureRedisInitialized();
      // Input validation
      if (!userId || typeof userId !== "string") {
        throw new ChatSDKError("bad_request:credit", "Invalid user ID");
      }

      if (!amount || amount <= 0 || !Number.isInteger(amount)) {
        throw new ChatSDKError("bad_request:credit", "Amount must be a positive integer");
      }

      if (!description || typeof description !== "string" || description.trim().length === 0) {
        throw new ChatSDKError("bad_request:credit", "Description is required");
      }

      // Check rate limits
      await this.checkRateLimit(userId);

      const supabase = await createServerClient();
      // Call RPC function for atomic operation
      const { data, error } = await supabase.rpc("deduct_credit", {
        p_user_id: userId,
        p_amount: amount,
        p_description: description.trim(),
      });

      if (error) {
        console.error("RPC deduct_credit failed:", error);
        throw new CreditOperationError(`Failed to deduct credits: ${error.message}`, error);
      }

      const result = data as CreditDeductionResult;

      if (!result.success) {
        if (result.error?.includes("Insufficient credits")) {
          throw new ChatSDKError("forbidden:credit", result.error);
        }
        throw new CreditOperationError(result.error || "Credit deduction failed");
      }

      // Invalidate cache after successful deduction
      await this.invalidateCache(userId);

      return result;
    } catch (error) {
      if (error instanceof ChatSDKError || error instanceof CreditRateLimitError) {
        throw error;
      }
      console.error("Credit deduction failed:", error);
      throw new CreditOperationError("Credit deduction failed", error);
    }
  }

  async getUserCredits(userId: string): Promise<number> {
    try {
      await this.ensureRedisInitialized();
      if (!userId || typeof userId !== "string") {
        throw new ChatSDKError("bad_request:credit", "Invalid user ID");
      }

      // Try cache first
      const cachedCredits = await this.getCachedCredits(userId);
      if (cachedCredits !== null) {
        return cachedCredits;
      }

      // Fallback to database
      const supabase = await createServerClient();
      const { data, error } = await supabase.rpc("get_user_credits", {
        p_user_id: userId,
      });

      if (error) {
        console.error("RPC get_user_credits failed:", error);
        throw new CreditOperationError(`Failed to get user credits: ${error.message}`, error);
      }

      const credits = data as number;

      // Cache the result
      await this.setCachedCredits(userId, credits);

      return credits;
    } catch (error) {
      if (error instanceof ChatSDKError) {
        throw error;
      }
      console.error("Failed to get user credits:", error);
      throw new CreditOperationError("Failed to get user credits", error);
    }
  }

  async addCredits(userId: string, amount: number, description: string): Promise<CreditAdditionResult> {
    try {
      console.log("CreditService.addCredits called with:", { userId, amount, description });
      await this.ensureRedisInitialized();

      // Input validation
      if (!userId || typeof userId !== "string") {
        console.error("Invalid user ID:", userId);
        throw new ChatSDKError("bad_request:credit", "Invalid user ID");
      }

      if (!amount || amount <= 0 || !Number.isInteger(amount)) {
        console.error("Invalid amount:", amount);
        throw new ChatSDKError("bad_request:credit", "Amount must be a positive integer");
      }

      if (!description || typeof description !== "string" || description.trim().length === 0) {
        console.error("Invalid description:", description);
        throw new ChatSDKError("bad_request:credit", "Description is required");
      }

      console.log("Calling RPC add_credits with:", {
        p_user_id: userId,
        p_amount: amount,
        p_description: description.trim(),
      });

      const supabase = await createServerClient();
      // Call RPC function for atomic operation
      const { data, error } = await supabase.rpc("add_credits", {
        p_user_id: userId,
        p_amount: amount,
        p_description: description.trim(),
      });

      console.log("RPC add_credits result:", { data, error });

      if (error) {
        console.error("RPC add_credits failed:", error);
        throw new CreditOperationError(`Failed to add credits: ${error.message}`, error);
      }

      const result = data as CreditAdditionResult;
      console.log("Credit addition result:", JSON.stringify(result, null, 2));

      if (!result.success) {
        console.error("Credit addition failed:", result.error);
        throw new CreditOperationError(result.error || "Credit addition failed");
      }

      // Invalidate cache after successful addition
      await this.invalidateCache(userId);
      console.log("Cache invalidated for user:", userId);

      return result;
    } catch (error) {
      if (error instanceof ChatSDKError) {
        throw error;
      }
      console.error("Credit addition failed:", error);
      throw new CreditOperationError("Credit addition failed", error);
    }
  }

  async getCreditHistory(userId: string, limit = 50, offset = 0): Promise<CreditTransaction[]> {
    try {
      await this.ensureRedisInitialized();
      if (!userId || typeof userId !== "string") {
        throw new ChatSDKError("bad_request:credit", "Invalid user ID");
      }

      // Validate and sanitize pagination parameters
      const sanitizedLimit = Math.min(Math.max(1, limit || 50), CREDIT_CONSTANTS.MAX_TRANSACTION_HISTORY);
      const sanitizedOffset = Math.max(0, offset || 0);

      const supabase = await createServerClient();
      const { data, error } = await supabase.rpc("get_credit_history", {
        p_user_id: userId,
        p_limit: sanitizedLimit,
        p_offset: sanitizedOffset,
      });

      if (error) {
        console.error("RPC get_credit_history failed:", error);
        throw new CreditOperationError(`Failed to get credit history: ${error.message}`, error);
      }

      return (data as CreditTransaction[]) || [];
    } catch (error) {
      if (error instanceof ChatSDKError) {
        throw error;
      }
      console.error("Failed to get credit history:", error);
      throw new CreditOperationError("Failed to get credit history", error);
    }
  }

  async validateSufficientCredits(userId: string, amount: number): Promise<boolean> {
    try {
      await this.ensureRedisInitialized();
      if (!userId || typeof userId !== "string") {
        return false;
      }

      if (!amount || amount <= 0) {
        return false;
      }

      // Always check database for critical validation to avoid cache inconsistency
      // This ensures we have the most up-to-date credit balance for validation
      console.log(`[CREDIT SERVICE] Validating credits for user ${userId}: required=${amount} (bypassing cache)`);

      const supabase = await createServerClient();
      const { data, error } = await supabase.rpc("validate_sufficient_credits", {
        p_user_id: userId,
        p_required_amount: amount,
      });

      if (error) {
        console.error("RPC validate_sufficient_credits failed:", error);
        return false;
      }

      const hasCredits = data as boolean;
      console.log(`[CREDIT SERVICE] Database validation result for user ${userId}: hasCredits=${hasCredits}`);

      // If validation succeeds, update cache with fresh data for future reads
      if (hasCredits) {
        try {
          const currentCredits = await supabase.rpc("get_user_credits", {
            p_user_id: userId,
          });
          if (!currentCredits.error && typeof currentCredits.data === "number") {
            await this.setCachedCredits(userId, currentCredits.data);
          }
        } catch (cacheError) {
          // Don't fail validation if cache update fails
          console.warn("Failed to update cache after validation:", cacheError);
        }
      }

      return hasCredits;
    } catch (error) {
      console.error("Failed to validate credits:", error);
      return false;
    }
  }

  /**
   * Cleanup method for graceful shutdown
   */
  async cleanup(): Promise<void> {
    if (this.redis) {
      try {
        await this.redis.quit();
        console.log("Redis connection closed for credit service");
      } catch (error) {
        console.error("Error closing Redis connection:", error);
      }
    }
  }
}

// Singleton instance for server-side use (lazy initialization)
let _serverCreditService: ServerCreditService | null = null;

export function getServerCreditService(): ServerCreditService {
  if (!_serverCreditService) {
    _serverCreditService = new ServerCreditService();

    // Cleanup on process exit
    if (typeof process !== "undefined") {
      process.on("beforeExit", async () => {
        if (_serverCreditService) {
          await _serverCreditService.cleanup();
        }
      });
    }
  }
  return _serverCreditService;
}

// Export the factory function as the main export
export const serverCreditService = getServerCreditService;

/**
 * Client-side Credit Service
 * Used in browser components with real-time updates and optimistic UI
 */
export class ClientCreditService implements ICreditService {
  private supabase;
  private creditCache = new Map<string, { credits: number; timestamp: number }>();
  private readonly CACHE_TTL = 30000; // 30 seconds for client-side cache

  constructor() {
    this.supabase = createBrowserClient();
  }

  /**
   * Get cached credits (client-side memory cache)
   */
  private getCachedCredits(userId: string): number | null {
    const cached = this.creditCache.get(userId);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.credits;
    }
    this.creditCache.delete(userId);
    return null;
  }

  /**
   * Set cached credits (client-side memory cache)
   */
  private setCachedCredits(userId: string, credits: number): void {
    this.creditCache.set(userId, {
      credits,
      timestamp: Date.now(),
    });
  }

  /**
   * Subscribe to real-time credit updates
   */
  subscribeToCredits(userId: string, callback: (credits: number) => void): () => void {
    const channel = this.supabase
      .channel(`credit_updates_${userId}`)
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "User",
          filter: `id=eq.${userId}`,
        },
        (payload) => {
          if (payload.new && "credits" in payload.new) {
            const newCredits = payload.new.credits as number;
            this.setCachedCredits(userId, newCredits);
            callback(newCredits);
          }
        }
      )
      .subscribe();

    return () => {
      channel.unsubscribe();
    };
  }

  async deductCredit(userId: string, amount: number, description: string): Promise<CreditDeductionResult> {
    try {
      // Input validation
      if (!userId || typeof userId !== "string") {
        throw new ChatSDKError("bad_request:credit", "Invalid user ID");
      }

      if (!amount || amount <= 0 || !Number.isInteger(amount)) {
        throw new ChatSDKError("bad_request:credit", "Amount must be a positive integer");
      }

      if (!description || typeof description !== "string" || description.trim().length === 0) {
        throw new ChatSDKError("bad_request:credit", "Description is required");
      }

      // Call RPC function
      const { data, error } = await this.supabase.rpc("deduct_credit", {
        p_user_id: userId,
        p_amount: amount,
        p_description: description.trim(),
      });

      if (error) {
        console.error("RPC deduct_credit failed:", error);
        throw new CreditOperationError(`Failed to deduct credits: ${error.message}`, error);
      }

      const result = data as CreditDeductionResult;

      if (!result.success) {
        if (result.error?.includes("Insufficient credits")) {
          throw new ChatSDKError("forbidden:credit", result.error);
        }
        throw new CreditOperationError(result.error || "Credit deduction failed");
      }

      // Update local cache
      this.setCachedCredits(userId, result.balance);

      return result;
    } catch (error) {
      if (error instanceof ChatSDKError) {
        throw error;
      }
      console.error("Credit deduction failed:", error);
      throw new CreditOperationError("Credit deduction failed", error);
    }
  }

  async getUserCredits(userId: string): Promise<number> {
    try {
      if (!userId || typeof userId !== "string") {
        throw new ChatSDKError("bad_request:credit", "Invalid user ID");
      }

      // Try cache first
      const cachedCredits = this.getCachedCredits(userId);
      if (cachedCredits !== null) {
        return cachedCredits;
      }

      // Fallback to database
      const { data, error } = await this.supabase.rpc("get_user_credits", {
        p_user_id: userId,
      });

      if (error) {
        console.error("RPC get_user_credits failed:", error);
        throw new CreditOperationError(`Failed to get user credits: ${error.message}`, error);
      }

      const credits = data as number;

      // Cache the result
      this.setCachedCredits(userId, credits);

      return credits;
    } catch (error) {
      if (error instanceof ChatSDKError) {
        throw error;
      }
      console.error("Failed to get user credits:", error);
      throw new CreditOperationError("Failed to get user credits", error);
    }
  }

  async addCredits(userId: string, amount: number, description: string): Promise<CreditAdditionResult> {
    try {
      // Input validation
      if (!userId || typeof userId !== "string") {
        throw new ChatSDKError("bad_request:credit", "Invalid user ID");
      }

      if (!amount || amount <= 0 || !Number.isInteger(amount)) {
        throw new ChatSDKError("bad_request:credit", "Amount must be a positive integer");
      }

      if (!description || typeof description !== "string" || description.trim().length === 0) {
        throw new ChatSDKError("bad_request:credit", "Description is required");
      }

      // Call RPC function
      const { data, error } = await this.supabase.rpc("add_credits", {
        p_user_id: userId,
        p_amount: amount,
        p_description: description.trim(),
      });

      if (error) {
        console.error("RPC add_credits failed:", error);
        throw new CreditOperationError(`Failed to add credits: ${error.message}`, error);
      }

      const result = data as CreditAdditionResult;

      if (!result.success) {
        throw new CreditOperationError(result.error || "Credit addition failed");
      }

      // Update local cache
      this.setCachedCredits(userId, result.balance);

      return result;
    } catch (error) {
      if (error instanceof ChatSDKError) {
        throw error;
      }
      console.error("Credit addition failed:", error);
      throw new CreditOperationError("Credit addition failed", error);
    }
  }

  async getCreditHistory(userId: string, limit = 50, offset = 0): Promise<CreditTransaction[]> {
    try {
      if (!userId || typeof userId !== "string") {
        throw new ChatSDKError("bad_request:credit", "Invalid user ID");
      }

      // Validate and sanitize pagination parameters
      const sanitizedLimit = Math.min(Math.max(1, limit || 50), CREDIT_CONSTANTS.MAX_TRANSACTION_HISTORY);
      const sanitizedOffset = Math.max(0, offset || 0);

      const { data, error } = await this.supabase.rpc("get_credit_history", {
        p_user_id: userId,
        p_limit: sanitizedLimit,
        p_offset: sanitizedOffset,
      });

      if (error) {
        console.error("RPC get_credit_history failed:", error);
        throw new CreditOperationError(`Failed to get credit history: ${error.message}`, error);
      }

      return (data as CreditTransaction[]) || [];
    } catch (error) {
      if (error instanceof ChatSDKError) {
        throw error;
      }
      console.error("Failed to get credit history:", error);
      throw new CreditOperationError("Failed to get credit history", error);
    }
  }

  async validateSufficientCredits(userId: string, amount: number): Promise<boolean> {
    try {
      if (!userId || typeof userId !== "string") {
        return false;
      }

      if (!amount || amount <= 0) {
        return false;
      }

      // Try cache first for quick validation
      const cachedCredits = this.getCachedCredits(userId);
      if (cachedCredits !== null) {
        return cachedCredits >= amount;
      }

      // Fallback to RPC function
      const { data, error } = await this.supabase.rpc("validate_sufficient_credits", {
        p_user_id: userId,
        p_required_amount: amount,
      });

      if (error) {
        console.error("RPC validate_sufficient_credits failed:", error);
        return false;
      }

      return data as boolean;
    } catch (error) {
      console.error("Failed to validate credits:", error);
      return false;
    }
  }

  /**
   * Clear local cache
   */
  clearCache(): void {
    this.creditCache.clear();
  }

  /**
   * Clear cache for a specific user
   */
  clearUserCache(userId: string): void {
    this.creditCache.delete(userId);
  }
}

// Export singleton instance for client-side use
export const clientCreditService = new ClientCreditService();

// Export factory functions for different contexts
export function createServerCreditService(): ServerCreditService {
  return new ServerCreditService();
}

export function createClientCreditService(): ClientCreditService {
  return new ClientCreditService();
}

// Export the appropriate service based on environment
export const creditService = typeof window === "undefined" ? getServerCreditService : clientCreditService;
