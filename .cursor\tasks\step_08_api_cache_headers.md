# Task 8: API Route Cache Headers

**Priority**: Medium
**Estimated Time**: 3 hours
**Status**: Pending
**Dependencies**: Task 2, Task 7

## Objective

Standardize cache headers across API routes with conditional caching for dynamic content and CDN-friendly configurations.

## Background

Current API caching issues:
- Inconsistent cache headers across routes
- Missing conditional caching for dynamic content
- No CDN optimization for static responses
- Potential for inappropriate caching of sensitive data

## Implementation Plan

### 1. Cache Header Utility (1 hour)

**File**: `lib/utils/cache-headers.ts`

Create standardized cache header utilities:

```typescript
export interface CacheConfig {
  maxAge?: number;
  sMaxAge?: number;
  staleWhileRevalidate?: number;
  mustRevalidate?: boolean;
  noCache?: boolean;
  noStore?: boolean;
  private?: boolean;
  public?: boolean;
  immutable?: boolean;
}

export function buildCacheControl(config: CacheConfig): string {
  const directives: string[] = [];
  
  if (config.noStore) {
    directives.push('no-store');
  } else if (config.noCache) {
    directives.push('no-cache');
  } else {
    if (config.private) {
      directives.push('private');
    } else if (config.public) {
      directives.push('public');
    }
    
    if (config.maxAge !== undefined) {
      directives.push(`max-age=${config.maxAge}`);
    }
    
    if (config.sMaxAge !== undefined) {
      directives.push(`s-maxage=${config.sMaxAge}`);
    }
    
    if (config.staleWhileRevalidate !== undefined) {
      directives.push(`stale-while-revalidate=${config.staleWhileRevalidate}`);
    }
    
    if (config.mustRevalidate) {
      directives.push('must-revalidate');
    }
    
    if (config.immutable) {
      directives.push('immutable');
    }
  }
  
  return directives.join(', ');
}

// Predefined cache configurations
export const CACHE_CONFIGS = {
  // No caching for sensitive data
  NO_CACHE: {
    noStore: true,
    private: true
  },
  
  // Short-term caching for dynamic data
  SHORT_TERM: {
    private: true,
    maxAge: 30,
    staleWhileRevalidate: 60
  },
  
  // Medium-term caching for semi-static data
  MEDIUM_TERM: {
    private: true,
    maxAge: 300, // 5 minutes
    sMaxAge: 300,
    staleWhileRevalidate: 600 // 10 minutes
  },
  
  // Long-term caching for static data
  LONG_TERM: {
    public: true,
    maxAge: 3600, // 1 hour
    sMaxAge: 3600,
    staleWhileRevalidate: 7200 // 2 hours
  },
  
  // Immutable caching for versioned assets
  IMMUTABLE: {
    public: true,
    maxAge: 31536000, // 1 year
    immutable: true
  }
} as const;

export function getCacheHeaders(config: CacheConfig): Record<string, string> {
  return {
    'Cache-Control': buildCacheControl(config),
    'Vary': 'Accept-Encoding, Authorization'
  };
}
```

### 2. API Route Cache Standardization (1.5 hours)

**File**: `app/api/credits/[userId]/route.ts`
```typescript
import { getCacheHeaders, CACHE_CONFIGS } from '@/lib/utils/cache-headers';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // ... existing logic ...
    
    return NextResponse.json({ credits }, {
      headers: getCacheHeaders(CACHE_CONFIGS.SHORT_TERM)
    });
  } catch (error) {
    // ... error handling
  }
}
```

**File**: `app/(chat)/api/history/route.ts`
```typescript
import { getCacheHeaders, CACHE_CONFIGS } from '@/lib/utils/cache-headers';

export async function GET(request: NextRequest) {
  try {
    // ... existing logic ...
    
    return Response.json(chats, {
      headers: getCacheHeaders(CACHE_CONFIGS.SHORT_TERM)
    });
  } catch (error) {
    // ... error handling
  }
}
```

**File**: `app/api/document/route.ts`
```typescript
import { getCacheHeaders, CACHE_CONFIGS } from '@/lib/utils/cache-headers';

export async function GET(request: Request) {
  try {
    // ... existing logic ...
    
    return Response.json(documents, {
      headers: getCacheHeaders(CACHE_CONFIGS.MEDIUM_TERM)
    });
  } catch (error) {
    // ... error handling
  }
}

export async function POST(request: Request) {
  try {
    // ... existing logic ...
    
    return Response.json(savedDocument, {
      headers: getCacheHeaders(CACHE_CONFIGS.NO_CACHE)
    });
  } catch (error) {
    // ... error handling
  }
}
```

### 3. Conditional Caching Middleware (0.5 hours)

**File**: `lib/middleware/cache-middleware.ts`

Create conditional caching based on request context:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getCacheHeaders, CACHE_CONFIGS } from '@/lib/utils/cache-headers';

export function withConditionalCaching(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const response = await handler(request);
    
    // Don't cache if there's an error
    if (!response.ok) {
      response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
      return response;
    }
    
    // Don't cache if user is not authenticated for private data
    const hasAuth = request.headers.get('authorization') || request.cookies.get('sb-access-token');
    
    if (!hasAuth && isPrivateRoute(request.url)) {
      response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
      return response;
    }
    
    // Apply default caching if no cache headers are set
    if (!response.headers.get('Cache-Control')) {
      const cacheHeaders = getCacheHeaders(CACHE_CONFIGS.SHORT_TERM);
      Object.entries(cacheHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
    }
    
    return response;
  };
}

function isPrivateRoute(url: string): boolean {
  const privatePatterns = [
    '/api/credits/',
    '/api/chat/',
    '/api/history',
    '/api/document'
  ];
  
  return privatePatterns.some(pattern => url.includes(pattern));
}
```

## Code Changes

### 1. Update Existing API Routes

**File**: `app/api/analytics/route.ts`
```typescript
import { getCacheHeaders, CACHE_CONFIGS } from '@/lib/utils/cache-headers';

export async function GET(request: NextRequest) {
  try {
    // ... existing logic ...
    
    return NextResponse.json({
      config: {
        disable_session_recording: true,
        autocapture: false,
      },
      featureFlags: {},
      sessionRecording: { endpoint: "" },
    }, {
      headers: getCacheHeaders(CACHE_CONFIGS.MEDIUM_TERM)
    });
  } catch (error) {
    // ... error handling
  }
}
```

**File**: `app/api/version/route.ts`
```typescript
import { getCacheHeaders, CACHE_CONFIGS } from '@/lib/utils/cache-headers';

export async function GET() {
  const versionInfo = {
    version: APP_VERSION,
    timestamp: BUILD_TIMESTAMP,
    buildTime: new Date(Number(BUILD_TIMESTAMP)).toISOString(),
  };

  return NextResponse.json(versionInfo, {
    headers: getCacheHeaders(CACHE_CONFIGS.NO_CACHE)
  });
}
```

### 2. Create Cache Configuration API

**File**: `app/api/cache/config/route.ts`
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getCacheHeaders, CACHE_CONFIGS } from '@/lib/utils/cache-headers';

export async function GET(request: NextRequest) {
  const cacheConfig = {
    configs: Object.keys(CACHE_CONFIGS),
    defaultTtl: {
      shortTerm: 30,
      mediumTerm: 300,
      longTerm: 3600
    },
    headers: {
      vary: 'Accept-Encoding, Authorization',
      cacheControl: 'Cache-Control'
    }
  };
  
  return NextResponse.json(cacheConfig, {
    headers: getCacheHeaders(CACHE_CONFIGS.LONG_TERM)
  });
}
```

### 3. Enhanced Next.js Config

**File**: `next.config.ts`
```typescript
const nextConfig: NextConfig = {
  // ... existing config ...
  
  async headers() {
    return [
      // ... existing headers ...
      
      // API routes default caching
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Vary',
            value: 'Accept-Encoding, Authorization'
          }
        ]
      },
      
      // Static assets long-term caching
      {
        source: '/images/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      },
      
      // Manifest and PWA files
      {
        source: '/manifest.webmanifest',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400' // 24 hours
          }
        ]
      }
    ];
  }
};
```

## Testing Strategy

1. **Cache Header Testing**:
   - Verify correct headers for different route types
   - Test conditional caching logic
   - Ensure sensitive data isn't cached

2. **CDN Testing**:
   - Test cache behavior with CDN
   - Verify cache invalidation works
   - Test edge case scenarios

3. **Performance Testing**:
   - Measure cache hit rates
   - Test response times with caching
   - Monitor CDN performance

## Success Criteria

- [ ] Consistent cache headers across all API routes
- [ ] Appropriate caching for different data types
- [ ] CDN-friendly cache configurations
- [ ] No inappropriate caching of sensitive data
- [ ] Improved API response times

## Rollback Plan

1. Remove cache header utilities
2. Return to manual cache header setting
3. Disable conditional caching middleware
4. Monitor for any caching issues

## Next Steps

- Move to Task 9: Cache Performance Monitoring
- Implement CDN cache monitoring
- Fine-tune cache configurations based on usage

## Notes

- Test thoroughly with different user scenarios
- Monitor for any security implications
- Ensure cache headers don't conflict with authentication
- Consider regional CDN variations
