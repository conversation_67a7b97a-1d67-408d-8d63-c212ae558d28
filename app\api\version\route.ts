// Route segment configuration for version API
export const dynamic = "force-dynamic";

import { NextResponse } from "next/server";
import { APP_VERSION, BUILD_TIMESTAMP } from "@/lib/version";

export async function GET() {
  const versionInfo = {
    version: APP_VERSION,
    timestamp: BUILD_TIMESTAMP,
    buildTime: new Date(Number(BUILD_TIMESTAMP)).toISOString(),
  };

  return NextResponse.json(versionInfo, {
    headers: {
      "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
      Pragma: "no-cache",
      Expires: "0",
      "Surrogate-Control": "no-store",
    },
  });
}
