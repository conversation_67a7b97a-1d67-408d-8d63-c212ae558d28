/**
 * User search and filter interface for admin dashboard
 * Provides search, filtering, and sorting capabilities for user management
 */

"use client";

import { useState, useCallback } from "react";
import { Search, Filter, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import type { UserSearchOptions } from "@/lib/services/admin-service";

interface UserSearchProps {
  onSearch: (options: UserSearchOptions) => void;
  isLoading?: boolean;
  totalResults?: number;
}

interface FilterState {
  search: string;
  minCredits: string;
  maxCredits: string;
}

export function UserSearch({ onSearch, isLoading, totalResults }: UserSearchProps) {
  const [filters, setFilters] = useState<FilterState>({
    search: "",
    minCredits: "",
    maxCredits: "",
  });

  const [appliedFilters, setAppliedFilters] = useState<FilterState>({
    search: "",
    minCredits: "",
    maxCredits: "",
  });

  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Convert filter state to search options
  const filtersToOptions = useCallback((filterState: FilterState): UserSearchOptions => {
    const options: UserSearchOptions = {};

    if (filterState.search.trim()) {
      options.search = filterState.search.trim();
    }

    if (filterState.minCredits && !Number.isNaN(Number(filterState.minCredits))) {
      options.minCredits = Number(filterState.minCredits);
    }

    if (filterState.maxCredits && !Number.isNaN(Number(filterState.maxCredits))) {
      options.maxCredits = Number(filterState.maxCredits);
    }

    return options;
  }, []);

  // Handle search input change (only update local state)
  const handleSearchChange = useCallback((value: string) => {
    setFilters((prev) => ({ ...prev, search: value }));
  }, []);

  // Handle search execution (on Enter key press)
  const handleSearchExecute = useCallback(() => {
    const newFilters = { ...filters };
    setAppliedFilters(newFilters);
    onSearch(filtersToOptions(newFilters));
  }, [filters, onSearch, filtersToOptions]);

  // Handle Enter key press in search input
  const handleSearchKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter") {
        e.preventDefault();
        handleSearchExecute();
      }
    },
    [handleSearchExecute]
  );

  // Apply filters
  const handleApplyFilters = useCallback(() => {
    setAppliedFilters(filters);
    onSearch(filtersToOptions(filters));
    setIsFilterOpen(false);
  }, [filters, onSearch, filtersToOptions]);

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    const emptyFilters = { search: "", minCredits: "", maxCredits: "" };
    setFilters(emptyFilters);
    setAppliedFilters(emptyFilters);
    onSearch({});
    setIsFilterOpen(false);
  }, [onSearch]);

  // Remove specific filter
  const handleRemoveFilter = useCallback(
    (filterType: keyof FilterState) => {
      const newFilters = { ...appliedFilters, [filterType]: "" };
      setFilters(newFilters);
      setAppliedFilters(newFilters);
      onSearch(filtersToOptions(newFilters));
    },
    [appliedFilters, onSearch, filtersToOptions]
  );

  // Count active filters (excluding search)
  const activeFilterCount = [appliedFilters.minCredits, appliedFilters.maxCredits].filter(Boolean).length;

  return (
    <div className="space-y-4">
      {/* Search and Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search Input */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 size-4 text-muted-foreground" />
          <Input
            placeholder="Search users by email... (Press Enter to search)"
            value={filters.search}
            onChange={(e) => handleSearchChange(e.target.value)}
            onKeyDown={handleSearchKeyDown}
            className="pl-10"
            disabled={isLoading}
          />
        </div>

        {/* Filter Button */}
        <Button variant="outline" className="relative" onClick={() => setIsFilterOpen(!isFilterOpen)}>
          <Filter className="size-4 mr-2" />
          Filters
          {activeFilterCount > 0 && (
            <Badge variant="secondary" className="ml-2 size-5 p-0 text-xs">
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </div>

      {/* Collapsible Filter Section */}
      {isFilterOpen && (
        <div className="border rounded-lg p-4 space-y-4 bg-muted/50">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Filter Users</h4>
            <Button variant="ghost" size="sm" onClick={handleClearFilters} className="h-auto p-1 text-muted-foreground hover:text-foreground">
              Clear all
            </Button>
          </div>

          {/* Credit Range Filters */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Credit Balance</Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor="minCredits" className="text-xs text-muted-foreground">
                  Min Credits
                </Label>
                <Input
                  id="minCredits"
                  type="number"
                  placeholder="0"
                  value={filters.minCredits}
                  onChange={(e) => setFilters((prev) => ({ ...prev, minCredits: e.target.value }))}
                  min="0"
                />
              </div>
              <div>
                <Label htmlFor="maxCredits" className="text-xs text-muted-foreground">
                  Max Credits
                </Label>
                <Input
                  id="maxCredits"
                  type="number"
                  placeholder="100"
                  value={filters.maxCredits}
                  onChange={(e) => setFilters((prev) => ({ ...prev, maxCredits: e.target.value }))}
                  min="0"
                />
              </div>
            </div>
          </div>

          {/* Apply Button */}
          <Button onClick={handleApplyFilters} className="w-full">
            Apply Filters
          </Button>
        </div>
      )}

      {/* Active Filters Display */}
      {(appliedFilters.search || appliedFilters.minCredits || appliedFilters.maxCredits) && (
        <div className="flex flex-wrap gap-2">
          {appliedFilters.search && (
            <Badge variant="secondary" className="gap-1">
              Search: &quot;{appliedFilters.search}&quot;
              <Button variant="ghost" size="sm" className="h-auto p-0 ml-1" onClick={() => handleRemoveFilter("search")}>
                <X className="size-3" />
              </Button>
            </Badge>
          )}

          {appliedFilters.minCredits && (
            <Badge variant="secondary" className="gap-1">
              Min Credits: {appliedFilters.minCredits}
              <Button variant="ghost" size="sm" className="h-auto p-0 ml-1" onClick={() => handleRemoveFilter("minCredits")}>
                <X className="size-3" />
              </Button>
            </Badge>
          )}

          {appliedFilters.maxCredits && (
            <Badge variant="secondary" className="gap-1">
              Max Credits: {appliedFilters.maxCredits}
              <Button variant="ghost" size="sm" className="h-auto p-0 ml-1" onClick={() => handleRemoveFilter("maxCredits")}>
                <X className="size-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}

      {/* Results Summary */}
      {totalResults !== undefined && (
        <div className="text-sm text-muted-foreground">
          {isLoading ? "Searching..." : `${totalResults} user${totalResults !== 1 ? "s" : ""} found`}
        </div>
      )}
    </div>
  );
}
