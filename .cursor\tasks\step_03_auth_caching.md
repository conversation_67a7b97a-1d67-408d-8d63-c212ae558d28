# Task 3: Authentication Caching Optimization

**Priority**: High
**Estimated Time**: 5 hours
**Status**: Pending
**Dependencies**: Task 1, Task 2

## Objective

Optimize authentication data caching while maintaining security, focusing on middleware performance and session management.

## Background

Current authentication issues:
- Session data fetched on every request in middleware
- User data fetched fresh in layouts
- No caching coordination between server and client
- Performance bottleneck in authentication flow

## Implementation Plan

### 1. Middleware Optimization (2 hours)

**File**: `lib/supabase/middleware.ts`

Implement smart caching for session validation:

```typescript
import { unstable_cache } from 'next/cache';
import { NextRequest, NextResponse } from 'next/server';

// Cache session validation with very short TTL for security
const getCachedSessionValidation = unstable_cache(
  async (sessionToken: string) => {
    const supabase = createServerClient(/* ... */);
    const { data: { user }, error } = await supabase.auth.getUser();
    
    return {
      user,
      error,
      timestamp: Date.now()
    };
  },
  ['session-validation'],
  {
    revalidate: 30, // 30 seconds - short for security
    tags: ['auth-session']
  }
);

export async function updateSession(request: NextRequest) {
  // Extract session token from cookies
  const sessionToken = request.cookies.get('sb-access-token')?.value;
  
  if (!sessionToken) {
    // No session token, proceed with unauthenticated flow
    return handleUnauthenticatedRequest(request);
  }
  
  try {
    // Use cached session validation
    const sessionData = await getCachedSessionValidation(sessionToken);
    
    // Check if cached data is still fresh (additional security layer)
    const isDataFresh = Date.now() - sessionData.timestamp < 30000; // 30 seconds
    
    if (!isDataFresh || sessionData.error || !sessionData.user) {
      return handleUnauthenticatedRequest(request);
    }
    
    // Session is valid, continue with authenticated flow
    return handleAuthenticatedRequest(request, sessionData.user);
    
  } catch (error) {
    console.error('Session validation error:', error);
    return handleUnauthenticatedRequest(request);
  }
}
```

### 2. Server Session Caching (1.5 hours)

**File**: `app/(auth)/auth-server.ts`

Enhance session caching with proper invalidation:

```typescript
import { unstable_cache } from 'next/cache';
import { revalidateTag } from 'next/cache';

// Cache user session data with security considerations
const getCachedUserSession = unstable_cache(
  async (userId: string) => {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user || user.id !== userId) {
      return null;
    }
    
    // Get credits with caching
    let credits = 0;
    try {
      const creditService = getServerCreditService();
      credits = await creditService.getUserCredits(user.id);
    } catch (error) {
      console.error("Failed to fetch user credits:", error);
    }
    
    const isAdmin = isEmailAdmin(user.email);
    
    return {
      user: {
        id: user.id,
        email: user.email,
        type: "regular" as const,
        credits,
        isAdmin,
      },
    };
  },
  ['user-session-data'],
  {
    revalidate: 60, // 1 minute
    tags: ['user-session']
  }
);

export async function auth(): Promise<SupabaseSession | null> {
  "use server";
  
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    return null;
  }
  
  // Use cached session data
  try {
    const sessionData = await getCachedUserSession(user.id);
    return sessionData;
  } catch (error) {
    console.error("Failed to get cached session:", error);
    // Fallback to direct fetch
    return getDirectUserSession(user.id);
  }
}

// Cache invalidation helper
export async function invalidateUserSession(userId: string) {
  revalidateTag('user-session');
  revalidateTag(`user-session-${userId}`);
}
```

### 3. Client-Side Auth Context Optimization (1.5 hours)

**File**: `lib/auth/auth-context.tsx`

Optimize client-side caching coordination:

```typescript
// Add cache coordination with server
const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  // ... existing state ...
  
  // Add cache synchronization
  const syncWithServerCache = useCallback(async () => {
    if (!user?.id) return;
    
    try {
      // Trigger server cache refresh
      await fetch(`/api/auth/refresh-cache`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: user.id })
      });
    } catch (error) {
      console.error('Failed to sync server cache:', error);
    }
  }, [user?.id]);
  
  // Enhanced refresh function with cache coordination
  const refreshAuth = useCallback(async () => {
    setIsLoadingAuth(true);
    setAuthError(null);
    
    try {
      const { data: { session: currentSession } } = await supabase.auth.getSession();
      
      if (currentSession?.user) {
        // Sync with server cache
        await syncWithServerCache();
        
        // Get fresh credits
        let userCredits = 0;
        try {
          setIsLoadingCredits(true);
          userCredits = await clientCreditService.getUserCredits(currentSession.user.id);
        } catch (error) {
          console.error("Failed to fetch credits during auth refresh:", error);
        } finally {
          setIsLoadingCredits(false);
        }
        
        const convertedUser = convertUser(currentSession.user, userCredits);
        setUser(convertedUser);
        setCredits(userCredits);
        
        if (convertedUser) {
          setSession({ user: convertedUser });
        }
      } else {
        setUser(null);
        setSession(null);
        setCredits(0);
      }
    } catch (error) {
      console.error("Auth refresh failed:", error);
      setAuthError("Failed to refresh authentication");
    } finally {
      setIsLoadingAuth(false);
    }
  }, [supabase.auth, convertUser, syncWithServerCache]);
  
  // ... rest of the component
};
```

## Code Changes

### 1. Create Cache Refresh API

**File**: `app/api/auth/refresh-cache/route.ts`
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag } from 'next/cache';
import { getServerSession } from '@/app/(auth)/auth-server';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const { userId } = await request.json();
    
    if (session.user.id !== userId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    // Invalidate user-specific caches
    revalidateTag('user-session');
    revalidateTag(`user-session-${userId}`);
    revalidateTag(`credits-${userId}`);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Cache refresh error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### 2. Enhanced Middleware

**File**: `middleware.ts`
```typescript
import type { NextRequest } from "next/server";
import { updateSession } from "@/lib/supabase/middleware";

export async function middleware(request: NextRequest) {
  // Add cache headers for auth-related requests
  const response = await updateSession(request);
  
  // Add cache control headers for authenticated routes
  if (request.nextUrl.pathname.startsWith('/(chat)')) {
    response.headers.set(
      'Cache-Control',
      'private, no-cache, no-store, must-revalidate'
    );
  }
  
  return response;
}
```

## Testing Strategy

1. **Security Testing**:
   - Verify session data doesn't leak between users
   - Test cache invalidation on logout
   - Ensure sensitive data isn't cached inappropriately

2. **Performance Testing**:
   - Measure middleware execution time
   - Test authentication flow performance
   - Monitor cache hit rates

3. **Functionality Testing**:
   - Test login/logout flows
   - Verify session persistence
   - Test cache invalidation scenarios

## Success Criteria

- [ ] Middleware performance improved by 40%+
- [ ] Authentication flow faster while maintaining security
- [ ] Proper cache invalidation on auth state changes
- [ ] No security vulnerabilities introduced
- [ ] Session data remains consistent

## Security Considerations

- Short TTL for session validation (30 seconds max)
- No caching of sensitive authentication tokens
- Proper cache invalidation on logout
- User isolation in cached data

## Rollback Plan

1. Remove caching from middleware
2. Disable session data caching
3. Return to direct Supabase calls
4. Monitor for any auth issues

## Next Steps

- Move to Task 4: Credit System Cache Coordination
- Implement auth performance monitoring
- Fine-tune cache TTL based on security requirements

## Notes

- Security is paramount - prefer shorter cache times
- Monitor for any authentication edge cases
- Ensure cache invalidation covers all scenarios
- Test thoroughly with multiple users
