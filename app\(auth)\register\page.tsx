"use client";

// Route segment configuration for authentication
export const dynamic = "force-dynamic";
export const revalidate = 0;

import Link from "next/link";
import { useRouter } from "next/navigation";
import { useActionState, useEffect, useState } from "react";
import { trackSignup } from "@/lib/analytics";

import { AuthForm } from "@/components/auth-form";
import { SubmitButton } from "@/components/submit-button";
import { GoogleSignInButton } from "@/components/google-sign-in-button";

import { register, type RegisterActionState } from "../actions";
import { toast } from "@/components/toast";
import { createClient } from "@/lib/supabase/client";

export default function Page() {
  const router = useRouter();

  const [email, setEmail] = useState("");
  const [isSuccessful, setIsSuccessful] = useState(false);

  const [state, formAction] = useActionState<RegisterActionState, FormData>(register, {
    status: "idle",
  });

  const supabase = createClient();

  useEffect(() => {
    // Guard against undefined state
    if (!state) return;

    if (state.status === "user_exists") {
      toast({ type: "error", description: "Account already exists!" });
    } else if (state.status === "failed") {
      toast({ type: "error", description: "Failed to create account!" });
    } else if (state.status === "invalid_data") {
      toast({
        type: "error",
        description: "Failed validating your submission!",
      });
    } else if (state.status === "verification_required") {
      // Show success toast and set successful state
      toast({ type: "success", description: "Account created! Please verify your email." });
      setIsSuccessful(true);

      // Redirect to the verification page with the email as a query parameter
      setTimeout(() => {
        router.push(`/auth/verify-email?email=${encodeURIComponent(email)}`);
      }, 1000); // Short delay to allow the toast to be seen
    } else if (state.status === "success") {
      // Show success toast and set successful state
      toast({ type: "success", description: "Account created successfully!" });
      setIsSuccessful(true);

      // Identify user in PostHog and track signup
      supabase.auth.getUser().then(({ data }) => {
        if (data.user) {
          trackSignup(data.user.id, "regular", data.user.email || undefined);
        }
      });

      // Simply refresh the page - the middleware will handle redirection
      setTimeout(() => {
        window.location.reload();
      }, 1000); // Short delay to allow the toast to be seen
    }
  }, [state, router, email, supabase.auth]);

  const handleSubmit = (formData: FormData) => {
    setEmail(formData.get("email") as string);
    formAction(formData);
  };

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl gap-12 flex flex-col">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h1 className="app-title dark:text-zinc-50">Bát Tự Master V</h1>
          <h2 className="text-lg text-gray-700 dark:text-zinc-300 mb-4">Phán về số mệnh, cuộc đời, tình duyên, công việc</h2>
          <h3 className="text-xl font-semibold dark:text-zinc-50">Sign Up</h3>
          <p className="text-sm text-gray-500 dark:text-zinc-400">Create an account with your email and password</p>
        </div>
        <AuthForm action={handleSubmit} defaultEmail={email}>
          <SubmitButton isSuccessful={isSuccessful}>Sign Up</SubmitButton>

          <div className="relative my-4">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300 dark:border-gray-700" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-background text-gray-500 dark:text-gray-400">Or continue with</span>
            </div>
          </div>

          <GoogleSignInButton />

          <p className="text-center text-sm text-gray-600 mt-4 dark:text-zinc-400">
            {"Already have an account? "}
            <Link href="/login" className="font-semibold text-gray-800 hover:underline dark:text-zinc-200">
              Sign in
            </Link>
            {" instead."}
          </p>
        </AuthForm>
      </div>
    </div>
  );
}
