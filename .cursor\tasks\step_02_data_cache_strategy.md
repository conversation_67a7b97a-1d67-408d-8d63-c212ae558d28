# Task 2: Data Cache Strategy Implementation

**Priority**: Critical
**Estimated Time**: 6 hours
**Status**: Pending
**Dependencies**: Task 1 (Route Segment Configuration)

## Objective

Implement explicit fetch caching with proper revalidation strategies for database queries and external API calls.

## Background

Current issues:
- No explicit `fetch` caching configurations
- Database queries fetch fresh data every time
- Missing cache tags for targeted invalidation
- No coordination between server and client caching

## Implementation Plan

### 1. Database Query Caching (2.5 hours)

**File**: `lib/db/queries.ts`

Add caching to frequently accessed queries:

#### User Data Queries
```typescript
// Add cache configuration to getUserCredits equivalent
export async function getCachedUserCredits(userId: string) {
  const response = await fetch(`/api/users/${userId}/credits`, {
    next: { 
      revalidate: 30, // 30 seconds
      tags: [`user-credits-${userId}`]
    }
  });
  return response.json();
}
```

#### Chat Queries
```typescript
// Cache chat history with longer TTL
export async function getCachedChatById(id: string) {
  const response = await fetch(`/api/chats/${id}`, {
    next: { 
      revalidate: 60, // 1 minute
      tags: [`chat-${id}`]
    }
  });
  return response.json();
}

// Cache chat list with shorter TTL
export async function getCachedChatsByUserId(userId: string) {
  const response = await fetch(`/api/chats/user/${userId}`, {
    next: { 
      revalidate: 30, // 30 seconds
      tags: [`user-chats-${userId}`]
    }
  });
  return response.json();
}
```

### 2. Authentication Data Caching (2 hours)

**File**: `app/(auth)/auth-server.ts`

Implement caching for user session data:

```typescript
import { unstable_cache } from 'next/cache';

// Cache user session with short TTL for security
const getCachedUserSession = unstable_cache(
  async (userId: string) => {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user || user.id !== userId) return null;
    
    // Get user credits with caching
    let credits = 0;
    try {
      const creditService = getServerCreditService();
      credits = await creditService.getUserCredits(user.id);
    } catch (error) {
      console.error("Failed to fetch user credits:", error);
    }
    
    return {
      user: {
        id: user.id,
        email: user.email,
        type: "regular" as const,
        credits,
        isAdmin: isEmailAdmin(user.email),
      },
    };
  },
  ['user-session'],
  {
    revalidate: 60, // 1 minute
    tags: ['auth-session']
  }
);
```

### 3. Credit System Cache Integration (1.5 hours)

**File**: `lib/services/credit-service.ts`

Enhance existing Redis caching with Next.js Data Cache:

```typescript
// Add fetch-based caching layer
async getUserCreditsWithFetchCache(userId: string): Promise<number> {
  try {
    // Try Next.js Data Cache first
    const response = await fetch(`/internal/api/credits/${userId}`, {
      next: { 
        revalidate: 30,
        tags: [`credits-${userId}`]
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      return data.credits;
    }
  } catch (error) {
    console.warn("Fetch cache failed, falling back to Redis:", error);
  }
  
  // Fallback to existing Redis implementation
  return this.getUserCredits(userId);
}

// Add cache invalidation helper
async invalidateUserCreditsCache(userId: string): Promise<void> {
  // Invalidate Next.js cache
  revalidateTag(`credits-${userId}`);
  
  // Invalidate Redis cache
  await this.invalidateUserCache(userId);
}
```

## Code Changes

### 1. Create Internal API Routes

**File**: `app/api/internal/credits/[userId]/route.ts`
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getServerCreditService } from '@/lib/services/credit-service';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const creditService = getServerCreditService();
    const credits = await creditService.getUserCredits(params.userId);
    
    return NextResponse.json({ credits }, {
      headers: {
        'Cache-Control': 'public, s-maxage=30, stale-while-revalidate=60'
      }
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch credits' },
      { status: 500 }
    );
  }
}
```

### 2. Update Database Queries

**File**: `lib/db/queries.ts`
```typescript
import { unstable_cache } from 'next/cache';
import { revalidateTag } from 'next/cache';

// Cache frequently accessed chat data
export const getCachedChatById = unstable_cache(
  async (id: string) => {
    const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));
    return selectedChat;
  },
  ['chat-by-id'],
  {
    revalidate: 60,
    tags: ['chats']
  }
);

// Cache user's chat list
export const getCachedChatsByUserId = unstable_cache(
  async (userId: string, limit: number = 10) => {
    const chats = await db
      .select()
      .from(chat)
      .where(eq(chat.userId, userId))
      .orderBy(desc(chat.createdAt))
      .limit(limit);
    
    return chats;
  },
  ['chats-by-user'],
  {
    revalidate: 30,
    tags: ['user-chats']
  }
);

// Add cache invalidation for new chats
export async function saveChatWithCacheInvalidation(chatData: any) {
  const result = await saveChat(chatData);
  
  // Invalidate relevant caches
  revalidateTag('user-chats');
  revalidateTag(`user-chats-${chatData.userId}`);
  
  return result;
}
```

## Testing Strategy

1. **Cache Hit Testing**:
   - Verify cache hits for repeated requests
   - Test cache invalidation triggers
   - Monitor cache performance metrics

2. **Data Consistency**:
   - Ensure cached data matches fresh data
   - Test cache invalidation on updates
   - Verify real-time features still work

3. **Performance Testing**:
   - Measure query response times
   - Monitor cache hit ratios
   - Test under load conditions

## Success Criteria

- [ ] Database queries use appropriate caching
- [ ] Cache invalidation works correctly
- [ ] Performance improvements measurable
- [ ] Real-time features remain functional
- [ ] No data consistency issues

## Rollback Plan

1. Remove `unstable_cache` wrappers
2. Disable fetch caching configurations
3. Return to direct database queries
4. Monitor for any issues

## Next Steps

- Move to Task 3: Authentication Caching Optimization
- Implement cache monitoring
- Fine-tune revalidation times based on usage patterns

## Notes

- Start with conservative TTL values
- Monitor cache hit rates and adjust accordingly
- Ensure cache invalidation covers all update scenarios
- Maintain backward compatibility with existing code
