# Task 5: Chat History Caching

**Priority**: High
**Estimated Time**: 6 hours
**Status**: Pending
**Dependencies**: Task 2, Task 4

## Objective

Implement caching for chat messages and history while maintaining real-time updates and proper cache invalidation for streaming responses.

## Background

Current chat system issues:
- Chat messages fetched fresh on every page load
- No caching for chat history pagination
- Streaming responses not coordinated with cache
- Missing optimization for frequently accessed chats

## Implementation Plan

### 1. Chat Message Caching (2.5 hours)

**File**: `lib/db/queries.ts`

Implement caching for chat-related queries:

```typescript
import { unstable_cache } from 'next/cache';
import { revalidateTag } from 'next/cache';

// Cache chat messages with pagination support
export const getCachedMessagesByChatId = unstable_cache(
  async (chatId: string, limit: number = 50, offset: number = 0) => {
    const messages = await db
      .select()
      .from(message)
      .where(eq(message.chatId, chatId))
      .orderBy(asc(message.createdAt))
      .limit(limit)
      .offset(offset);
    
    return messages;
  },
  ['chat-messages'],
  {
    revalidate: 60, // 1 minute
    tags: ['chat-messages']
  }
);

// Cache chat metadata
export const getCachedChatById = unstable_cache(
  async (id: string) => {
    const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));
    return selectedChat;
  },
  ['chat-metadata'],
  {
    revalidate: 300, // 5 minutes (chat metadata changes less frequently)
    tags: ['chat-metadata']
  }
);

// Cache user's chat list with pagination
export const getCachedChatsByUserId = unstable_cache(
  async (userId: string, limit: number = 10, startingAfter?: string, endingBefore?: string) => {
    let query = db
      .select()
      .from(chat)
      .where(eq(chat.userId, userId))
      .orderBy(desc(chat.createdAt));
    
    if (startingAfter) {
      query = query.where(lt(chat.createdAt, new Date(startingAfter)));
    }
    
    if (endingBefore) {
      query = query.where(gt(chat.createdAt, new Date(endingBefore)));
    }
    
    const chats = await query.limit(limit + 1);
    const hasMore = chats.length > limit;
    
    return {
      chats: hasMore ? chats.slice(0, limit) : chats,
      hasMore
    };
  },
  ['user-chats'],
  {
    revalidate: 30, // 30 seconds
    tags: ['user-chats']
  }
);

// Enhanced save messages with cache invalidation
export async function saveMessagesWithCacheInvalidation(messages: Array<DBMessage>) {
  const result = await saveMessages({ messages });
  
  // Invalidate relevant caches
  const chatIds = [...new Set(messages.map(m => m.chatId))];
  
  for (const chatId of chatIds) {
    revalidateTag('chat-messages');
    revalidateTag(`chat-messages-${chatId}`);
  }
  
  return result;
}
```

### 2. Streaming Cache Coordination (2 hours)

**File**: `app/(chat)/api/chat/route.ts`

Coordinate streaming responses with cache invalidation:

```typescript
// Enhanced chat route with cache coordination
export async function POST(request: Request) {
  // ... existing setup ...
  
  try {
    const result = streamText({
      // ... existing configuration ...
      onFinish: async ({ text, toolCalls, toolResults, usage, finishReason }) => {
        // ... existing onFinish logic ...
        
        // Invalidate chat caches after message completion
        try {
          revalidateTag('chat-messages');
          revalidateTag(`chat-messages-${id}`);
          revalidateTag('user-chats');
          revalidateTag(`user-chats-${session.user.id}`);
        } catch (error) {
          console.error('Failed to invalidate chat caches:', error);
        }
      }
    });
    
    // ... rest of the implementation
  } catch (error) {
    // ... error handling
  }
}

// Enhanced GET route for resumable streams
export async function GET(request: Request) {
  // ... existing implementation ...
  
  // If no active stream, serve from cache
  if (!stream) {
    try {
      // Try to get cached messages
      const cachedMessages = await getCachedMessagesByChatId(chatId);
      const mostRecentMessage = cachedMessages.at(-1);
      
      if (mostRecentMessage && mostRecentMessage.role === "assistant") {
        const messageCreatedAt = new Date(mostRecentMessage.createdAt);
        
        if (differenceInSeconds(resumeRequestedAt, messageCreatedAt) <= 15) {
          // Serve cached message
          const restoredStream = createDataStream({
            execute: (buffer) => {
              buffer.writeData({
                type: "append-message",
                message: JSON.stringify(mostRecentMessage),
              });
            },
          });
          
          return new Response(restoredStream, { status: 200 });
        }
      }
    } catch (error) {
      console.warn('Failed to serve from cache, falling back to database:', error);
    }
    
    // Fallback to existing database query
    const messages = await getMessagesByChatId({ id: chatId });
    // ... rest of existing logic
  }
  
  return new Response(stream, { status: 200 });
}
```

### 3. Chat Page Optimization (1.5 hours)

**File**: `app/(chat)/chat/[id]/page.tsx`

Optimize chat page with caching:

```typescript
import { getCachedChatById, getCachedMessagesByChatId } from '@/lib/db/queries';

// Add route segment configuration
export const revalidate = 60; // 1 minute
export const fetchCache = 'default-cache';

export default async function Page({ params }: { params: { id: string } }) {
  const { id } = params;
  const session = await getServerSession();
  
  if (!session?.user) {
    redirect("/login");
  }
  
  // Use cached queries
  const [chat, messagesFromDb] = await Promise.all([
    getCachedChatById(id),
    getCachedMessagesByChatId(id)
  ]);
  
  if (!chat) {
    notFound();
  }
  
  // ... rest of existing logic
}
```

## Code Changes

### 1. Chat History API Route

**File**: `app/(chat)/api/history/route.ts`
```typescript
import { getCachedChatsByUserId } from '@/lib/db/queries';

// Add route segment configuration
export const revalidate = 30;
export const fetchCache = 'default-cache';

export async function GET(request: NextRequest) {
  const { searchParams } = request.nextUrl;
  
  const limit = Number.parseInt(searchParams.get("limit") || "10");
  const startingAfter = searchParams.get("starting_after");
  const endingBefore = searchParams.get("ending_before");
  
  if (startingAfter && endingBefore) {
    return new ChatSDKError("bad_request:api", "Only one of starting_after or ending_before can be provided.").toResponse();
  }
  
  const session = await getServerSession();
  
  if (!session?.user) {
    return new ChatSDKError("unauthorized:chat").toResponse();
  }
  
  // Use cached query
  const chats = await getCachedChatsByUserId(
    session.user.id,
    limit,
    startingAfter,
    endingBefore
  );
  
  return Response.json(chats, {
    headers: {
      'Cache-Control': 'private, s-maxage=30, stale-while-revalidate=60'
    }
  });
}
```

### 2. Message API Route

**File**: `app/api/messages/[chatId]/route.ts`
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getCachedMessagesByChatId } from '@/lib/db/queries';
import { getServerSession } from '@/app/(auth)/auth-server';

export const revalidate = 60;
export const fetchCache = 'default-cache';

export async function GET(
  request: NextRequest,
  { params }: { params: { chatId: string } }
) {
  try {
    const session = await getServerSession();
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const { searchParams } = request.nextUrl;
    const limit = Number.parseInt(searchParams.get('limit') || '50');
    const offset = Number.parseInt(searchParams.get('offset') || '0');
    
    // Verify user has access to this chat
    const chat = await getCachedChatById(params.chatId);
    
    if (!chat || chat.userId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    const messages = await getCachedMessagesByChatId(params.chatId, limit, offset);
    
    return NextResponse.json({ messages }, {
      headers: {
        'Cache-Control': 'private, s-maxage=60, stale-while-revalidate=120'
      }
    });
  } catch (error) {
    console.error('Message fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch messages' },
      { status: 500 }
    );
  }
}
```

## Testing Strategy

1. **Cache Consistency Testing**:
   - Verify new messages appear in cached results
   - Test cache invalidation after streaming completion
   - Ensure pagination works with caching

2. **Real-time Testing**:
   - Test streaming responses with cache coordination
   - Verify real-time updates don't break caching
   - Test concurrent chat operations

3. **Performance Testing**:
   - Measure chat loading performance
   - Test cache hit rates for chat history
   - Monitor streaming response times

## Success Criteria

- [ ] Chat history loads 40% faster
- [ ] Cache hit rate > 80% for chat messages
- [ ] Real-time streaming remains functional
- [ ] Pagination works correctly with caching
- [ ] No message duplication or loss

## Rollback Plan

1. Remove caching from chat queries
2. Disable cache invalidation in streaming
3. Return to direct database queries
4. Monitor for any chat functionality issues

## Next Steps

- Move to Task 6: Real-time Cache Invalidation
- Implement chat performance monitoring
- Optimize cache warming for active chats

## Notes

- Balance caching with real-time requirements
- Monitor cache invalidation timing carefully
- Ensure streaming responses don't break cache consistency
- Test thoroughly with multiple concurrent users
