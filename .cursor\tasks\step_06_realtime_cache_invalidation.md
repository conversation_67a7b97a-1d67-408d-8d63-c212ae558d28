# Task 6: Real-time Cache Invalidation

**Priority**: High
**Estimated Time**: 5 hours
**Status**: Pending
**Dependencies**: Task 3, Task 4, Task 5

## Objective

Set up comprehensive cache invalidation strategies for real-time features using `revalidatePath` and `revalidateTag`, coordinated with Supabase real-time subscriptions.

## Background

Current real-time issues:
- No systematic cache invalidation strategy
- Real-time updates may not reflect in cached data
- Missing coordination between Supabase subscriptions and Next.js cache
- Potential stale data in cached responses

## Implementation Plan

### 1. Supabase Real-time Cache Integration (2 hours)

**File**: `lib/realtime/cache-invalidation.ts`

Create a centralized cache invalidation service:

```typescript
import { revalidateTag, revalidatePath } from 'next/cache';
import { createClient } from '@/lib/supabase/server';

export class RealtimeCacheInvalidator {
  private supabase;
  private subscriptions: Map<string, any> = new Map();
  
  constructor() {
    this.supabase = createClient();
  }
  
  /**
   * Set up real-time subscriptions for cache invalidation
   */
  async initializeSubscriptions() {
    // User credits updates
    this.subscribeToUserCredits();
    
    // Chat messages updates
    this.subscribeToChatMessages();
    
    // Chat metadata updates
    this.subscribeToChatMetadata();
  }
  
  /**
   * Subscribe to user credit changes
   */
  private subscribeToUserCredits() {
    const subscription = this.supabase
      .channel('user_credits_cache')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'User',
          filter: 'credits=neq.null'
        },
        async (payload) => {
          if (payload.new && payload.new.id) {
            const userId = payload.new.id;
            
            // Invalidate credit-related caches
            await this.invalidateUserCredits(userId);
          }
        }
      )
      .subscribe();
    
    this.subscriptions.set('user_credits', subscription);
  }
  
  /**
   * Subscribe to chat message changes
   */
  private subscribeToChatMessages() {
    const subscription = this.supabase
      .channel('chat_messages_cache')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'Message'
        },
        async (payload) => {
          if (payload.new && payload.new.chatId) {
            const chatId = payload.new.chatId;
            
            // Invalidate message-related caches
            await this.invalidateChatMessages(chatId);
          }
        }
      )
      .subscribe();
    
    this.subscriptions.set('chat_messages', subscription);
  }
  
  /**
   * Subscribe to chat metadata changes
   */
  private subscribeToChatMetadata() {
    const subscription = this.supabase
      .channel('chat_metadata_cache')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'Chat'
        },
        async (payload) => {
          if (payload.new && payload.new.id) {
            const chatId = payload.new.id;
            const userId = payload.new.userId;
            
            // Invalidate chat-related caches
            await this.invalidateChatMetadata(chatId, userId);
          }
        }
      )
      .subscribe();
    
    this.subscriptions.set('chat_metadata', subscription);
  }
  
  /**
   * Invalidate user credit caches
   */
  async invalidateUserCredits(userId: string) {
    try {
      revalidateTag('credits');
      revalidateTag(`credits-${userId}`);
      revalidateTag('user-session');
      revalidateTag(`user-session-${userId}`);
      
      console.log(`Invalidated credit caches for user: ${userId}`);
    } catch (error) {
      console.error('Failed to invalidate user credit caches:', error);
    }
  }
  
  /**
   * Invalidate chat message caches
   */
  async invalidateChatMessages(chatId: string) {
    try {
      revalidateTag('chat-messages');
      revalidateTag(`chat-messages-${chatId}`);
      
      // Also invalidate the specific chat page
      revalidatePath(`/chat/${chatId}`);
      
      console.log(`Invalidated message caches for chat: ${chatId}`);
    } catch (error) {
      console.error('Failed to invalidate chat message caches:', error);
    }
  }
  
  /**
   * Invalidate chat metadata caches
   */
  async invalidateChatMetadata(chatId: string, userId: string) {
    try {
      revalidateTag('chat-metadata');
      revalidateTag(`chat-metadata-${chatId}`);
      revalidateTag('user-chats');
      revalidateTag(`user-chats-${userId}`);
      
      // Invalidate chat list and specific chat page
      revalidatePath('/');
      revalidatePath(`/chat/${chatId}`);
      
      console.log(`Invalidated metadata caches for chat: ${chatId}`);
    } catch (error) {
      console.error('Failed to invalidate chat metadata caches:', error);
    }
  }
  
  /**
   * Clean up subscriptions
   */
  async cleanup() {
    for (const [name, subscription] of this.subscriptions) {
      try {
        await subscription.unsubscribe();
        console.log(`Unsubscribed from ${name}`);
      } catch (error) {
        console.error(`Failed to unsubscribe from ${name}:`, error);
      }
    }
    this.subscriptions.clear();
  }
}

// Singleton instance
let cacheInvalidator: RealtimeCacheInvalidator | null = null;

export function getCacheInvalidator(): RealtimeCacheInvalidator {
  if (!cacheInvalidator) {
    cacheInvalidator = new RealtimeCacheInvalidator();
  }
  return cacheInvalidator;
}
```

### 2. Server Action Cache Invalidation (1.5 hours)

**File**: `app/(chat)/actions.ts`

Enhance server actions with cache invalidation:

```typescript
"use server";

import { revalidateTag, revalidatePath } from 'next/cache';
import { getCacheInvalidator } from '@/lib/realtime/cache-invalidation';

// Enhanced delete message action
export async function deleteMessageWithCacheInvalidation(
  chatId: string,
  messageId: string,
  timestamp: Date
) {
  try {
    // Perform the deletion
    await deleteMessagesByChatIdAfterTimestamp({
      id: chatId,
      timestamp
    });
    
    // Invalidate relevant caches
    revalidateTag('chat-messages');
    revalidateTag(`chat-messages-${chatId}`);
    revalidatePath(`/chat/${chatId}`);
    
    console.log(`Deleted message and invalidated caches for chat: ${chatId}`);
  } catch (error) {
    console.error('Failed to delete message with cache invalidation:', error);
    throw error;
  }
}

// Enhanced chat visibility update
export async function updateChatVisibilityWithCacheInvalidation(
  chatId: string,
  visibility: VisibilityType
) {
  try {
    // Update visibility
    await updateChatVisiblityById({
      chatId,
      visibility
    });
    
    // Invalidate relevant caches
    revalidateTag('chat-metadata');
    revalidateTag(`chat-metadata-${chatId}`);
    revalidateTag('user-chats');
    revalidatePath(`/chat/${chatId}`);
    
    console.log(`Updated chat visibility and invalidated caches for chat: ${chatId}`);
  } catch (error) {
    console.error('Failed to update chat visibility with cache invalidation:', error);
    throw error;
  }
}

// Enhanced save chat model action
export async function saveChatModelAsCookieWithCacheInvalidation(model: string) {
  try {
    await saveChatModelAsCookie(model);
    
    // Invalidate user-specific caches that might depend on model selection
    revalidatePath('/');
    
    console.log(`Updated chat model and invalidated relevant caches`);
  } catch (error) {
    console.error('Failed to save chat model with cache invalidation:', error);
    throw error;
  }
}
```

### 3. API Route Cache Invalidation (1.5 hours)

**File**: `app/api/cache/invalidate/route.ts`

Create a centralized cache invalidation API:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag, revalidatePath } from 'next/cache';
import { getServerSession } from '@/app/(auth)/auth-server';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const { type, targets, userId, chatId } = await request.json();
    
    // Verify user has permission to invalidate these caches
    if (userId && session.user.id !== userId && !session.user.isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    switch (type) {
      case 'user-credits':
        if (userId) {
          revalidateTag('credits');
          revalidateTag(`credits-${userId}`);
          revalidateTag('user-session');
          revalidateTag(`user-session-${userId}`);
        }
        break;
        
      case 'chat-messages':
        if (chatId) {
          revalidateTag('chat-messages');
          revalidateTag(`chat-messages-${chatId}`);
          revalidatePath(`/chat/${chatId}`);
        }
        break;
        
      case 'chat-metadata':
        if (chatId && userId) {
          revalidateTag('chat-metadata');
          revalidateTag(`chat-metadata-${chatId}`);
          revalidateTag('user-chats');
          revalidateTag(`user-chats-${userId}`);
          revalidatePath('/');
          revalidatePath(`/chat/${chatId}`);
        }
        break;
        
      case 'user-chats':
        if (userId) {
          revalidateTag('user-chats');
          revalidateTag(`user-chats-${userId}`);
          revalidatePath('/');
        }
        break;
        
      case 'all-user-data':
        if (userId) {
          // Invalidate all user-related caches
          revalidateTag('credits');
          revalidateTag(`credits-${userId}`);
          revalidateTag('user-session');
          revalidateTag(`user-session-${userId}`);
          revalidateTag('user-chats');
          revalidateTag(`user-chats-${userId}`);
          revalidatePath('/');
        }
        break;
        
      default:
        return NextResponse.json({ error: 'Invalid invalidation type' }, { status: 400 });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: `Cache invalidated for type: ${type}` 
    });
  } catch (error) {
    console.error('Cache invalidation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

## Code Changes

### 1. Initialize Real-time Cache Invalidation

**File**: `app/(chat)/layout.tsx`
```typescript
import { getCacheInvalidator } from '@/lib/realtime/cache-invalidation';

export default async function Layout({ children }: { children: React.ReactNode }) {
  // ... existing code ...
  
  // Initialize real-time cache invalidation
  const cacheInvalidator = getCacheInvalidator();
  await cacheInvalidator.initializeSubscriptions();
  
  return (
    <>
      {/* ... existing JSX ... */}
    </>
  );
}
```

### 2. Client-side Cache Invalidation Helper

**File**: `lib/utils/cache-invalidation.ts`
```typescript
export async function invalidateCache(
  type: string,
  options: {
    userId?: string;
    chatId?: string;
  } = {}
) {
  try {
    const response = await fetch('/api/cache/invalidate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type,
        ...options
      })
    });
    
    if (!response.ok) {
      throw new Error(`Cache invalidation failed: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Failed to invalidate cache:', error);
    throw error;
  }
}
```

## Testing Strategy

1. **Real-time Testing**:
   - Test cache invalidation triggers from Supabase changes
   - Verify real-time subscriptions work correctly
   - Test concurrent cache invalidation scenarios

2. **Cache Consistency Testing**:
   - Ensure invalidated caches reflect fresh data
   - Test cache invalidation timing
   - Verify no stale data persists

3. **Performance Testing**:
   - Monitor cache invalidation performance impact
   - Test subscription overhead
   - Measure real-time update latency

## Success Criteria

- [ ] Real-time cache invalidation working correctly
- [ ] No stale data in cached responses
- [ ] Cache invalidation triggers reliably
- [ ] Performance impact minimal
- [ ] Real-time features remain responsive

## Rollback Plan

1. Disable real-time cache invalidation
2. Remove Supabase subscription setup
3. Return to manual cache invalidation
4. Monitor for any real-time issues

## Next Steps

- Move to Task 7: Database Query Caching
- Implement cache invalidation monitoring
- Fine-tune invalidation timing and scope

## Notes

- Monitor subscription overhead carefully
- Ensure cache invalidation doesn't impact performance
- Test thoroughly with multiple concurrent users
- Consider rate limiting for cache invalidation API
